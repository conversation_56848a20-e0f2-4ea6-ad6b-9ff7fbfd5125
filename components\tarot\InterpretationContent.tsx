'use client'
import { Sparkles, Calendar } from 'lucide-react'
import Image from 'next/image'
import { useSearchParams } from 'next/navigation'
import { useTranslations, useLocale } from 'next-intl'
import React, { useMemo, useState, useEffect } from 'react'

import { useCards } from '@/lib/load-cards'

// 增强的文本处理函数
const formatInterpretationText = (text: string, keywords: Record<string, string[]>): React.ReactElement[] => {
  if (!text) return []

  // 按段落分割
  const paragraphs = text.split('\n\n').filter((p) => p.trim().length > 0)

  return paragraphs.map((paragraph, pIndex) => {
    let processedText = paragraph.trim()

    // 检查是否是标题段落（包含**或者以数字开头）
    const isTitle = /^\*\*.*\*\*$/.test(processedText) || /^[一二三四五六七八九十\d]+[、．.]/.test(processedText)

    // 移除markdown标记
    processedText = processedText.replace(/\*\*(.*?)\*\*/g, '$1')

    if (isTitle) {
      return (
        <div key={pIndex} className="mb-6">
          <h3 className="border-l-4 border-purple-500/50 bg-gradient-to-r from-purple-400 via-pink-400 to-purple-300 bg-clip-text py-2 pl-4 text-xl font-bold text-transparent shadow-sm">
            {processedText}
          </h3>
        </div>
      )
    }

    // 按句子分割，保留标点符号
    const sentences = processedText.split(/([。！？；：])/).filter((s) => s.length > 0)

    return (
      <div key={pIndex} className="group mb-4">
        <div className="text-base leading-relaxed transition-all duration-300 hover:text-gray-100">
          {sentences.map((sentence, sIndex) => {
            // 如果是标点符号，直接返回
            if (/^[。！？；：]$/.test(sentence)) {
              return (
                <span key={`punct-${pIndex}-${sIndex}`} className="text-gray-200">
                  {sentence}
                </span>
              )
            }

            if (sentence.trim().length === 0) return null

            // 处理句子中的关键词
            const sentenceText = sentence.trim()
            const keywordMatches: Array<{ start: number; end: number; word: string; type: string }> = []

            // 查找所有关键词
            Object.entries(keywords).forEach(([type, words]) => {
              ;(words as string[]).forEach((word) => {
                let index = 0
                while ((index = sentenceText.indexOf(word, index)) !== -1) {
                  // 检查是否与现有匹配重叠
                  const isOverlapping = keywordMatches.some(
                    (match) =>
                      (index >= match.start && index < match.end) ||
                      (index + word.length > match.start && index + word.length <= match.end)
                  )

                  if (!isOverlapping) {
                    keywordMatches.push({
                      start: index,
                      end: index + word.length,
                      word,
                      type
                    })
                  }
                  index += word.length
                }
              })
            })

            // 按位置排序
            keywordMatches.sort((a, b) => a.start - b.start)

            // 构建JSX元素
            const elements: React.ReactElement[] = []
            let lastIndex = 0

            keywordMatches.forEach((match, index) => {
              // 添加匹配前的普通文本
              if (match.start > lastIndex) {
                const normalText = sentenceText.slice(lastIndex, match.start)
                elements.push(
                  <span key={`text-${pIndex}-${sIndex}-${index}`} className="text-gray-200">
                    {normalText}
                  </span>
                )
              }

              // 添加高亮的关键词
              const keywordClass = getKeywordClass(match.type)
              elements.push(
                <span key={`keyword-${pIndex}-${sIndex}-${index}`} className={keywordClass}>
                  {match.word}
                </span>
              )

              lastIndex = match.end
            })

            // 添加剩余的普通文本
            if (lastIndex < sentenceText.length) {
              elements.push(
                <span key={`text-${pIndex}-${sIndex}-final`} className="text-gray-200">
                  {sentenceText.slice(lastIndex)}
                </span>
              )
            }

            // 检查句子类型并添加相应的样式
            const nextPunct = sentences[sIndex + 1]
            let sentenceClass = 'inline'

            if (nextPunct === '！') {
              sentenceClass += ' relative'
            } else if (nextPunct === '？') {
              sentenceClass += ' relative'
            }

            return (
              <span key={`sentence-${pIndex}-${sIndex}`} className={sentenceClass}>
                {elements.length > 0 ? elements : sentenceText}
                {/* 为重要句子添加小图标 */}
                {nextPunct === '！' && (
                  <span className="ml-1 inline-block animate-pulse text-amber-400 opacity-70">✨</span>
                )}
                {nextPunct === '？' && <span className="ml-1 inline-block text-blue-400 opacity-70">❓</span>}
              </span>
            )
          })}
        </div>
      </div>
    )
  })
}

// 获取关键词样式类
const getKeywordClass = (type: string): string => {
  const baseClass =
    'font-semibold px-2 py-1 rounded-lg transition-all duration-300 inline-block transform hover:scale-105 '

  switch (type) {
    case 'cards':
      return (
        baseClass +
        'bg-gradient-to-r from-purple-500/30 to-pink-500/30 text-purple-200 border border-purple-400/40 shadow-lg hover:shadow-purple-500/30 hover:bg-gradient-to-r hover:from-purple-500/40 hover:to-pink-500/40'
      )
    case 'emotions':
      return (
        baseClass +
        'bg-gradient-to-r from-rose-500/30 to-pink-500/30 text-rose-200 border border-rose-400/40 shadow-lg hover:shadow-rose-500/30 hover:bg-gradient-to-r hover:from-rose-500/40 hover:to-pink-500/40'
      )
    case 'career':
      return (
        baseClass +
        'bg-gradient-to-r from-emerald-500/30 to-green-500/30 text-emerald-200 border border-emerald-400/40 shadow-lg hover:shadow-emerald-500/30 hover:bg-gradient-to-r hover:from-emerald-500/40 hover:to-green-500/40'
      )
    case 'time':
      return (
        baseClass +
        'bg-gradient-to-r from-blue-500/30 to-cyan-500/30 text-blue-200 border border-blue-400/40 shadow-lg hover:shadow-blue-500/30 hover:bg-gradient-to-r hover:from-blue-500/40 hover:to-cyan-500/40'
      )
    case 'states':
      return (
        baseClass +
        'bg-gradient-to-r from-amber-500/30 to-orange-500/30 text-amber-200 border border-amber-400/40 shadow-lg hover:shadow-amber-500/30 hover:bg-gradient-to-r hover:from-amber-500/40 hover:to-orange-500/40'
      )
    case 'advice':
      return (
        baseClass +
        'bg-gradient-to-r from-violet-500/30 to-purple-500/30 text-violet-200 border border-violet-400/40 shadow-lg hover:shadow-violet-500/30 hover:bg-gradient-to-r hover:from-violet-500/40 hover:to-purple-500/40'
      )
    case 'emphasis':
      return (
        baseClass +
        'bg-gradient-to-r from-yellow-500/30 to-amber-500/30 text-yellow-200 border border-yellow-400/40 shadow-lg hover:shadow-yellow-500/30 hover:bg-gradient-to-r hover:from-yellow-500/40 hover:to-amber-500/40 animate-pulse shadow-yellow-500/20'
      )
    default:
      return baseClass + 'bg-gray-500/30 text-gray-200 border border-gray-400/40 shadow-lg hover:shadow-gray-500/30'
  }
}

const getKeywordsByLocale = async (locale: string) => {
  const response = await fetch(`/keywords/${locale}.json`)
  if (response.ok) {
    return await response.json()
  }
}

interface InterpretationContentProps {
  text: string
  isLoading: boolean
  cards?: CurrentCardType[]
  customQuestion?: string
  customSpreadName?: string
}

const InterpretationContent = ({
  text,
  isLoading,
  cards = [],
  customQuestion,
  customSpreadName
}: InterpretationContentProps) => {
  const tInterpretation = useTranslations('interpretation')
  const t = useTranslations()
  const locale = useLocale()
  const [keywords, setKeywords] = useState<Record<string, string[]>>({})
  const { cards: allCardsInfo } = useCards(locale)
  const [currentSessionCards, setCurrentSessionCards] = useState<CurrentCardType[]>([])

  useEffect(() => {
    if (cards && cards.length > 0) {
      try {
        const currentCards = cards.map((item: any) => ({
          ...allCardsInfo[item.index],
          position: item.position,
          flipped: item.flipped,
          direction: item.direction,
          index: item.index
        }))
        setCurrentSessionCards(currentCards)
      } catch (error) {
        console.error('Failed to parse session cards:', error)
        setCurrentSessionCards([])
      }
    } else {
      setCurrentSessionCards([])
    }
  }, [cards, allCardsInfo])

  useEffect(() => {
    getKeywordsByLocale(locale).then((data) => {
      if (data) setKeywords(data as Record<string, string[]>)
    })
  }, [locale])

  const searchParams = useSearchParams()

  // 从URL参数或props中获取信息
  const question =
    customQuestion || (searchParams.get('question') ? decodeURIComponent(searchParams.get('question')!) : '')
  const spreadName =
    customSpreadName || (searchParams.get('spreadName') ? decodeURIComponent(searchParams.get('spreadName')!) : '')

  // 解析文本，提取主要段落
  const parsedContent = useMemo(() => {
    if (!text) return { title: '', content: [] }

    // 寻找主标题（通常是第一个 ** 包围的内容）
    const titleMatch = text.match(/\*\*([^*]+)\*\*/)
    const mainTitle = titleMatch ? titleMatch[1].trim() : tInterpretation('title')

    // 按段落分割内容
    const paragraphs = text
      .replace(/\*\*[^*]+\*\*/g, '') // 移除所有标题标记
      .split('\n\n')
      .filter((p) => p.trim().length > 0)
      .map((p) => p.trim())

    return {
      title: mainTitle,
      content: paragraphs
    }
  }, [text, tInterpretation])

  const currentDate = new Date()
    .toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    })
    .replace(/\//g, ' / ')

  // 图例分组顺序
  const legendOrder = ['cards', 'emotions', 'career', 'time', 'states', 'advice', 'emphasis']

  return (
    <>
      {isLoading && !text ? (
        <div className="flex min-h-[300px] flex-col items-center justify-center">
          <div className="relative">
            <div className="h-20 w-20 rounded-full border-4 border-purple-500/30"></div>
            <div className="absolute top-0 h-20 w-20 animate-spin rounded-full border-4 border-transparent border-t-purple-500"></div>
            <Sparkles className="absolute top-1/2 left-1/2 h-8 w-8 -translate-x-1/2 -translate-y-1/2 text-purple-400" />
          </div>
          <p className="mt-6 text-lg text-purple-300">{tInterpretation('loadingMessage')}</p>
          <p className="mt-2 text-sm text-gray-400">{tInterpretation('loadingSubtitle')}</p>
        </div>
      ) : text ? (
        <div className="space-y-6">
          {/* 问题部分 */}
          {question && (
            <div className="rounded-xl border border-purple-400/20 bg-gradient-to-r from-black/40 to-purple-900/20 p-6 backdrop-blur-sm">
              <div className="mb-3 flex items-center justify-between">
                <h3 className="text-lg font-semibold text-purple-300">{tInterpretation('yourQuestion')}</h3>
                <div className="flex items-center gap-1 text-xs text-purple-400">
                  <Calendar className="h-3 w-3" />
                  <span>{currentDate}</span>
                </div>
              </div>
              <p className="text-base leading-relaxed text-gray-200">{question}</p>
            </div>
          )}

          {/* 卡牌展示区域 */}
          {currentSessionCards.length > 0 && (
            <div className="rounded-xl border border-purple-400/20 bg-gradient-to-r from-black/40 to-purple-900/20 p-6 backdrop-blur-sm">
              <h3 className="mb-4 text-lg font-semibold text-purple-300">{tInterpretation('drawnCards')}</h3>
              <div className="flex flex-wrap justify-center gap-4">
                {currentSessionCards.map((card, index) => (
                  <div key={index} className="flex flex-col items-center">
                    <div className="relative rounded-lg shadow-md">
                      <div className={`relative h-32 w-20 ${card.direction === 'reversed' ? 'rotate-180' : ''}`}>
                        {card.link && (
                          <Image src={card.link} alt={card.name} fill className="object-cover" sizes="80px" />
                        )}
                      </div>
                      {/* 位置标号 */}
                      <div className="absolute -top-2 -left-2 flex h-6 w-6 items-center justify-center rounded-full bg-gradient-to-r from-purple-500 to-pink-500 text-xs font-bold text-white shadow-lg">
                        {card.position}
                      </div>
                      {/* 逆位标识 */}
                      {card.direction === 'reversed' && (
                        <div className="absolute -top-2 -right-2 flex h-6 w-6 items-center justify-center rounded-full bg-red-500 text-xs font-bold text-white shadow-lg">
                          ↓
                        </div>
                      )}
                    </div>
                    {/* 卡牌名称 */}
                    <p className="mt-2 text-center text-xs text-purple-300">{card.name}</p>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* 解读标题 */}
          <div className="rounded-xl border border-purple-400/20 bg-gradient-to-r from-black/40 to-purple-900/20 p-6 backdrop-blur-sm">
            <h2 className="mb-2 text-2xl font-bold text-purple-200">
              {parsedContent.title || `${spreadName} - ${tInterpretation('spreadReading')}`}
            </h2>
            <div className="text-sm text-purple-400">{tInterpretation('aiDepthReading')}</div>
          </div>

          {/* 解读内容 */}
          <div className="rounded-xl border border-purple-400/20 bg-gradient-to-r from-black/40 to-purple-900/20 p-6 backdrop-blur-sm">
            {/* 关键词图例 */}
            <div className="mb-6 rounded-lg border border-purple-500/20 bg-black/20 p-4">
              <h4 className="mb-3 flex items-center gap-2 text-sm font-semibold text-purple-300">
                <Sparkles className="h-4 w-4" />
                {t('interpretationKeywordsLegend.legendTitle')}
              </h4>
              <div className="grid grid-cols-2 gap-2 text-xs md:grid-cols-3">
                {legendOrder.map((key) => (
                  <div key={key} className="flex items-center gap-2">
                    <span
                      className={
                        key === 'cards'
                          ? 'h-3 w-3 rounded-full border border-purple-400/50 bg-gradient-to-r from-purple-500/50 to-pink-500/50'
                          : key === 'emotions'
                            ? 'h-3 w-3 rounded-full border border-rose-400/50 bg-gradient-to-r from-rose-500/50 to-pink-500/50'
                            : key === 'career'
                              ? 'h-3 w-3 rounded-full border border-emerald-400/50 bg-gradient-to-r from-emerald-500/50 to-green-500/50'
                              : key === 'time'
                                ? 'h-3 w-3 rounded-full border border-blue-400/50 bg-gradient-to-r from-blue-500/50 to-cyan-500/50'
                                : key === 'states'
                                  ? 'h-3 w-3 rounded-full border border-amber-400/50 bg-gradient-to-r from-amber-500/50 to-orange-500/50'
                                  : key === 'advice'
                                    ? 'h-3 w-3 rounded-full border border-violet-400/50 bg-gradient-to-r from-violet-500/50 to-purple-500/50'
                                    : key === 'emphasis'
                                      ? 'h-3 w-3 rounded-full border border-yellow-400/50 bg-gradient-to-r from-yellow-500/50 to-amber-500/50'
                                      : 'h-3 w-3 rounded-full border border-gray-400/50 bg-gray-500/50'
                      }
                    ></span>
                    <span className="text-gray-300">{t(`interpretationKeywordsLegend.${key}` as any)}</span>
                  </div>
                ))}
              </div>
            </div>

            <div className="space-y-4">
              {Object.keys(keywords).length > 0 &&
                formatInterpretationText(parsedContent.content.join('\n\n'), keywords)}
            </div>

            {isLoading && (
              <div className="mt-6 flex items-center justify-center gap-3 rounded-lg bg-purple-900/30 p-4">
                <Sparkles className="h-5 w-5 animate-spin text-purple-400" />
                <span className="text-purple-300">{tInterpretation('generatingContent')}</span>
              </div>
            )}
          </div>
        </div>
      ) : (
        <div className="flex min-h-[200px] items-center justify-center">
          <div className="text-center">
            <Sparkles className="mx-auto h-12 w-12 text-purple-400 opacity-50" />
            <p className="mt-4 text-gray-400">{tInterpretation('flipAllCards')}</p>
          </div>
        </div>
      )}
    </>
  )
}

export default InterpretationContent
