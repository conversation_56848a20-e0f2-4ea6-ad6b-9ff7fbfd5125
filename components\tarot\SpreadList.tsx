'use client'

import Image from 'next/image'
import { useTranslations, useLocale } from 'next-intl'
import { useEffect, useState } from 'react'

import { Button } from '@/components/ui/button'
import { Link } from '@/i18n/navigation'
import { useSpreads } from '@/lib/load-spreads'
import { cn } from '@/lib/utils'

export default function SpreadList() {
  const [spreadList, setList] = useState<SpreadClass[]>([])
  const t = useTranslations('tarot')
  const common = useTranslations('common')

  const locale = useLocale()

  // 使用动态加载spreads
  const { spreads: spreadsData, loading: spreadsLoading } = useSpreads(locale)

  useEffect(() => {
    if (!spreadsLoading && spreadsData) {
      setList(spreadsData)
    }
  }, [spreadsData, spreadsLoading])

  // 如果spreads正在加载，显示加载状态
  if (spreadsLoading) {
    return (
      <div className="space-y-16">
        <div className="text-center text-white">
          <div className="inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-purple-400 border-r-transparent"></div>
          <p className="mt-4">{common('loadingSpread')}</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-16">
      {spreadList.map((item, index) => {
        const isImageLeft = index % 2 === 1

        return (
          <div
            key={item.type}
            className={cn(
              'mx-auto flex max-w-5xl flex-col items-center gap-8 rounded-xl bg-black/30 p-8 backdrop-blur-sm md:flex-row md:gap-12',
              isImageLeft ? 'md:flex-row-reverse' : ''
            )}
          >
            <div className="w-full md:w-1/2">
              <div className="relative overflow-hidden rounded-xl">
                <Image
                  src={item.picture}
                  alt={item.type}
                  width={500}
                  height={300}
                  className="h-auto w-full rounded-lg object-cover shadow-md"
                />
              </div>
            </div>

            <div className="w-full space-y-4 text-center md:w-1/2 md:text-left">
              <h2 className="text-3xl font-bold text-white">{item.type}</h2>
              <p className="text-gray-300">{item.desc}</p>
              <Link href={`/question?type=${item.route}`}>
                <Button size="lg" className="rounded-lg bg-purple-600 text-base text-white hover:bg-purple-700">
                  {t('start')}
                </Button>
              </Link>
            </div>
          </div>
        )
      })}
    </div>
  )
}
