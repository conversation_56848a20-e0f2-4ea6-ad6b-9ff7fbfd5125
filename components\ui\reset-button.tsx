'use client'

import { Button } from './button'

interface ResetButtonProps {
  className?: string
}

export function ResetButton({ className }: ResetButtonProps) {
  const handleReset = () => {
    const form = document.querySelector('form') as HTMLFormElement
    if (form) {
      form.reset()
      // 重置表单后重定向到当前页面以清除 URL 参数
      window.location.href = window.location.pathname
    }
  }

  return (
    <Button type="button" variant="outline" onClick={handleReset} className={className}>
      重置
    </Button>
  )
}
