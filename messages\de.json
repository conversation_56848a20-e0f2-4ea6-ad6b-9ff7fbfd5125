{"headers": {"blogs": "Blog", "home": "Startseite", "about": "<PERSON><PERSON>", "submitTools": "Tool e<PERSON><PERSON>ichen", "navigation": "Website-Navigation", "navigationDescription": "Besuchen Sie die wichtigsten Bereiche unserer Website"}, "siteInfo": {"brandName": "Destiny AI", "meta": {"title": "Destiny AI - KI-<PERSON><PERSON>-<PERSON><PERSON><PERSON>ge-<PERSON>l", "description": "Entdecken Sie die besten KI-gestützten Wahrsage-Tools wie Tarotkarten, Astrologie, I Ging usw. Ihr Leitfaden zum Verständnis moderner Wahrsagetechniken."}}, "divinationCategories": {"tarot": {"name": "<PERSON><PERSON><PERSON><PERSON>leg<PERSON>", "description": "Eine Methode der Wahrsagerei, bei der ein Kartenspiel mit 78 Karten mit symbolischen Bildern verwendet wird, um Einblicke in Vergangenheit, Gegenwart und Zukunft zu erhalten.", "origin": "Italien/Frankreich (Europa des 15. Jahrhunderts)", "seoTitle": "KI-Tarotkartenlegung - Kostenlose Online-Kartenbedeutungen und -Vorhersagen", "seoDescription": "Erhalten Sie online eine präzise, KI-gestützte Tarotkartenlegung. Nutzen Sie unser fortschrittliches Tarotkarten-Vorhersagetool, um das Liebesleben, die Karrierewege und die Zukunft zu erkunden, die die Karten enthüllen."}, "astrology": {"name": "Astrologie", "description": "Studium, wie die Positionen und Bewegungen von Himmelskörpern menschliche Angelegenheiten und Naturphänomene beeinflussen.", "origin": "Mehrfache Ursprünge (Altbabylon, Ägypten, Griechenland)", "seoTitle": "KI-Astrologie-Charts und Horoskope – personalisierte Tierkreiszeichen-Interpretationen", "seoDescription": "Erkunde dein kosmisches Schicksal mit KI-gestützten Astrologie-Tools. Erhalte personalisierte Geburtshoroskope, Tageshoroskope und Partnerinterpretationen basierend auf deinem Sternzeichen."}, "iChing": {"name": "<PERSON> Ging", "description": "Ein altes chinesisches Wahrsagesystem, das Hexagramme verwendet, um Anleitungen zu geben und zukünftige Ereignisse vorherzusagen.", "origin": "China (ca. 1000–750 v. Chr.)", "seoTitle": "KI-I-Ging-Orakel – I-Ging-Interpretationen und Hexagramme", "seoDescription": "Konsultiere das alte I Ging mit unserer KI-I-Ging-Orakel. Erhalte personalisierte Hexagramm-Interpretationen, Erläuterungen und Anleitungen für wichtige Lebensentscheidungen."}, "numerology": {"name": "Numerologie", "description": "Der Glaube an eine heilige oder mystische Verbindung zwischen Zahlen und Ereignissen, die durch Berechnungen Informationen über Persönlichkeit und Zukunft enthüllt.", "origin": "<PERSON><PERSON><PERSON> Ursprünge (Altgriechenland, Ägypten, Babylon)", "seoTitle": "KI-Numerologie-Rechner - Lebensweg- und Schicksalszahlenanalyse", "seoDescription": "Berechnen Sie mit unserem KI-Numerologie-Tool Ihren Lebensweg, Ihr Schicksal und Ihre Ausdruckszahl. Entdecken Sie, wie Zahlen Ihre Persönlichkeit, Beziehungen und Zukunft beeinflussen."}, "palmistry": {"name": "Handflächenlesen", "description": "Menschen erkennen und die Zukunft vorhersagen, indem man die Linien, Formen und Texturen der Handfläche studiert.", "origin": "Indien (ca. 4000 Jahre alt)", "seoTitle": "KI-Handlinienanalyse – Numerologie und Handanalyse", "seoDescription": "Laden Sie ein Foto hoch, um eine KI-Handlinienanalyse durchzuführen. Entdecken Sie mit unseren digitalen Handlinienerkennungswerkzeugen, wie Ihre Handlinien Ihr Liebesleben, Ihren beruflichen Erfolg, Ihre Gesundheit und Ihre Zukunft offenbaren."}, "dreamInterpretation": {"name": "Traumdeutung", "description": "Analysieren Sie Träume, um verborgene Bedeutungen, Informationen und Vorhersagen für die Zukunft aufzudecken.", "origin": "Altes Ägypten, Griechenland und Mesopotamien", "seoTitle": "KI-Traumanalyse – Bedeutung von Träumen und Symbolen", "seoDescription": "Verstehen Sie Ihre Träume durch KI-Traumanalyse. Erhalten Sie personalisierte Interpretationen von Traumsymbolen, wiederkehrenden Themen und unbewussten Informationen im Schlaf."}, "vedic": {"name": "Vedische Astrologie", "description": "Ein altes indisches Astrologiesystem, das auf Sternpositionen und nicht auf Regressionspositionen basiert und eine detaillierte Analy<PERSON> von <PERSON>, Charakter und Lebensereignissen bietet.", "origin": "Indien (ca. 1500 v. Chr.)", "seoTitle": "KI Vedische Astrologie - Jyotish-Geburtsdiagramm und Vorhersagen", "seoDescription": "Entdecke deinen wahren kosmischen Bauplan durch KI-vedische Astrologie. Erhalte genaue Jyotish-Interpretationen, ein Geburtshoroskop und Vorhersagen für Karriere, Beziehungen und Lebensweg."}, "categoryNotFound": "Kategorie nicht gefunden", "categoryNotFoundDescription": "Die von Ihnen gesuchte Wahrsagekategorie wurde nicht gefunden. Bitte besuchen Sie unsere Homepage, um verfügbare Kategorien zu finden.", "other": {"name": "<PERSON><PERSON>", "description": "Verschiedene alternative Wahrsagetechniken und mystische Praktiken, die nicht von traditionellen Kategorien abgedeckt werden.", "origin": "Verschiedene Kulturen auf der ganzen Welt", "seoTitle": "Alternative KI-Wahrsage-Tools - Einzigartige Methoden der Wahrsagerei", "seoDescription": "Entdecken Sie unkonventionelle KI-Wahrsagemethoden und einzigartige Wahrsagetechniken. Entdecken Sie seltene mystische Praktiken und alternative Wege, um das Schicksal zu ergründen."}, "comprehensive": {"name": "Umfassend", "description": "<PERSON>e Plattform, die mehrere Wahrsagemethoden und integrierte Methoden der Wahrsagerei an einem Ort bietet.", "origin": "Moderne Integration traditioneller Praktiken", "seoTitle": "Integrierte KI-Wahrsageplattform - Vielfältige Methoden der Wahrsagerei", "seoDescription": "Greifen Sie an einem Ort auf mehrere KI-Wahrsagemethoden zu. Die umfassende Plattform bietet Tarot, Astrologie, Numerologie und mehr und bietet umfassende spirituelle Beratung und Wahrsagedienste."}}, "footer": {"description": "Destiny AI - Ihr umfassendes KI-gestütztes Tarot-Tool.", "contact": {"title": "Kontaktieren Sie uns", "intro": "Bei Fragen kontaktieren Sie bitte:", "email": "<EMAIL>"}, "quickLinks": {"title": "Schnelllinks", "home": "Startseite", "aboutUs": "Über uns", "refundPolicy": "Rückerstattungsrichtlinie", "subscriptionTerms": "Abonnementbedingungen"}, "categories": "Kategorisierung", "copyright": "© 2025 Destiny AI Tools Alle Rechte vorbehalten."}, "login": {"signOut": "Abmelden", "login": "Anmelden", "modal": {"description": "Melden Sie sich an, um Ihre Reise fortzusetzen", "triggerText": "Anmelden"}, "form": {"continueWithGoogle": "Mit Google fortfahren", "continueWithGithub": "<PERSON><PERSON>", "orContinueWith": "Oder fortfahren", "emailPlaceholder": "Geben Sie Ihre E-Mail-Adresse ein", "signInWithEmail": "Mit E-Mail anmelden"}}, "common": {"loading": "Wird geladen...", "cancel": "Abbrechen", "close": "Schließen", "previous": "Vorherige Seite", "next": "Nächste Seite", "save": "Speichern", "edit": "<PERSON><PERSON><PERSON>", "delete": "Löschen", "submit": "<PERSON><PERSON><PERSON><PERSON>", "morePages": "Weitere Seiten anzeigen", "language": "<PERSON><PERSON><PERSON>", "selectLanguage": "Sprache auswählen", "loadingSpread": "<PERSON><PERSON>..."}, "tarot": {"title": "KI-Tarot-Interpretation", "description": "Erkunde das Unbekannte, erhalte Führung und erschließe innere Weisheit.", "start": "<PERSON><PERSON><PERSON> eine <PERSON>-Lesung", "startAi": "Starte eine KI-Tarotkarten-Lesung", "desc": "Einführung", "upright": "Aufrecht", "reversed": "Umgekehrt"}, "question": {"title": "Wählen Sie Ihr Tarotkarten-Layout", "description": "<PERSON><PERSON>en Sie die Frage ein, die Si<PERSON> vorhersagen möchten, und die KI empfiehlt Ihnen das am besten geeignete Layout.", "placeholder": "Bitte beschreiben Si<PERSON> die Frage, die Si<PERSON> gerne beantwortet haben möchten, z. B.: Wie kann sich meine Karriere in der Zukunft entwickeln, um erfolgreich zu sein?", "isLogin": "Starte die Wahrsagerei-Reise nach dem Einloggen", "isRecommending": "Problem wird analysiert...", "start": "<PERSON><PERSON>en Si<PERSON> eine Frage ein, um eine KI-empfoh<PERSON> zu erhalten", "dialogTitle": "KI-gestützte intelligente Empfehlung", "recommendation": "<PERSON><PERSON><PERSON><PERSON>", "startDraw": "Beginne mit dem Ziehen der Karten", "reSelectSpread": "Wählen Sie selbst andere Kartenanordnungen aus", "selectSpread": "<PERSON><PERSON><PERSON><PERSON> dies<PERSON>", "login": "Anmelden", "loginDescription": "Bitte melde dich an, um die Wahrsagungsfunktion weiterhin nutzen zu können.", "spreadInfo": {"title": "Kartenlayout-Interpretation", "positionMeaning": "Bedeutung der Positionen", "meaning": "Bedeutung", "understood": "<PERSON>ch verstehe"}}, "draw": {"dialogTitle": "Wahrsageinformationen", "title": "Personalisierte KI-Interpretation", "viewResult": "Interpretationsergebnisse anzeigen", "loading": "Interpretation wird für Si<PERSON> generiert, bitte warten Sie...", "error": "Entschuldigung, bei der Interpretation ist ein Fehler aufgetreten, bitte versuchen Si<PERSON> es später noch einmal.", "turnAll": "Bitte alle Karten aufdecken", "selectCard": "<PERSON><PERSON> m<PERSON> {count} <PERSON><PERSON> au<PERSON>en", "selected": "Ausgewählt: {current} / {total}", "leftRight": "Karten links und rechts anzeigen", "complete": "Kartenauswahl abgeschlossen! Der Kartenaufdeckungsabschnitt wird aufgerufen...", "shuffle": "Mischen", "shuffleDesc": "Bitte lange drücken und ziehen, um zu mischen"}, "interpretation": {"title": "KI-Tarot-Interpretation", "loadingMessage": "KI entschlüsselt für Sie die Geheimnisse der Tarotkarten...", "loadingSubtitle": "<PERSON><PERSON> warten, <PERSON><PERSON><PERSON> versammelt sich", "yourQuestion": "<PERSON><PERSON><PERSON>:", "drawnCards": "Gez<PERSON><PERSON>:", "spreadReading": "Kartenanordnung Interpretation", "aiDepthReading": "KI-Tiefeninterpretation • Exklusiv für Einzelpersonen", "generatingContent": "Die Interpretation wird generiert...", "flipAllCards": "<PERSON>te drehen Sie alle Karten um und klicken Sie dann auf die Schaltfläche 'Interpretation'."}, "interpretationKeywordsLegend": {"legendTitle": "Schlüsselwortlegende", "cards": "<PERSON><PERSON><PERSON><PERSON>", "emotions": "Emotion", "career": "Karriere", "time": "Zeit", "states": "Zustand", "advice": "Vorschlag", "emphasis": "Betonung"}, "profile": {"title": "Persönliches Zentrum", "loginRequired": "<PERSON>te zu<PERSON>t anmelden, um das persönliche Zentrum anzuzeigen", "loading": "Wird geladen...", "description": "Erforsche dein digitales Schicksal, verfolge deine mysteriöse Reise", "tokenUsage": {"title": "Token-Nutzung", "remaining": "Verbleibende Token", "used": "Verwendete Token", "total": "Gesamt-Token", "unlimited": "Unbegrenzt"}, "personalInfo": {"title": "Persönliche Informationen", "birthday": "Geburtstag", "gender": "Geschlecht", "male": "<PERSON><PERSON><PERSON><PERSON>", "female": "<PERSON><PERSON><PERSON>", "other": "<PERSON><PERSON>", "notSet": "<PERSON>cht festgelegt", "edit": "<PERSON><PERSON><PERSON>", "save": "Speichern", "cancel": "Abbrechen", "birthdayPlaceholder": "Bitte wählen Sie Ihr Geburtsdatum aus", "genderPlaceholder": "Bitte wählen Sie das Geschlecht aus"}, "tarotHistory": {"title": "Wahrsagungsaufzeichnungen", "noRecords": "<PERSON><PERSON>rsagungsaufzeichnungen vorhanden", "question": "Frage", "spread": "Kartenlayout", "status": "Zustand", "date": "Zeit", "viewDetails": "Details anzeigen", "statusCreated": "<PERSON><PERSON><PERSON><PERSON>", "statusDrawing": "Karten werden gezogen", "statusCompleted": "Abgeschlossen", "loadMore": "<PERSON><PERSON> <PERSON>"}, "settings": {"title": "Kontoeinstellungen", "logout": "Abmelden"}}, "records": {"title": "Wahrsagungsaufzeichnungen", "description": "Verfolge deine mysteriöse Reise und erinnere dich an jede Fügung des Schicksals.", "totalRecords": "Insgesamt {count} Einträge", "noRecords": "<PERSON><PERSON>rsagungsaufzeichnungen vorhanden", "startJourney": "Beginne deine erste <PERSON>-Lesereise", "table": {"question": "Wahrsagefrage", "spread": "Kartenlegenamen", "category": "Kartenlegekategorie", "cardCount": "<PERSON><PERSON><PERSON><PERSON>", "status": "Zustand", "createdAt": "Erstellungszeit", "completedAt": "Fertigstellungszeit", "actions": "Operation", "viewDetails": "Details"}, "status": {"completed": "Abgeschlossen", "drawing": "Karten werden gezogen", "created": "<PERSON><PERSON><PERSON><PERSON>", "unknown": "Unbekannt"}, "subtitle": "Verfolge deine mysteriöse Reise und erinnere dich an jede Fügung des Schicksals.", "startFirstReading": "Beginne deine erste <PERSON>-Lesereise", "startDivination": "Wahrsagerei beginnen", "loading": "Wird geladen...", "viewDetails": "Details anzeigen", "view": "Anzeigen", "delete": "Löschen", "deleteConfirm": "Möchten Sie diesen Eintrag wirklich löschen?", "deleteSuccess": "Erfolg<PERSON><PERSON>", "cards": "<PERSON><PERSON>", "showing": "Anzeigen", "recordsText": "Einträge", "detail": {"title": "占卜记录详情", "question": "占卜问题：", "spread": "牌阵：", "category": "类别：", "cardCount": "卡牌数量：", "status": "状态：", "createdAt": "创建时间：", "completedAt": "完成时间："}, "page": {"continueDivination": "<PERSON><PERSON> wahr<PERSON>gen", "deleteError": "Löschen fehlgeschlagen, bitte versuche es erneut", "deleteConfirm": "Möchten Sie diesen Eintrag wirklich löschen?"}}, "home": {"hero": {"title": "KI-Tarotkarten", "subtitle": "<PERSON><PERSON><PERSON><PERSON>", "description": "Verschmelzung von altem Wissen mit moderner KI-Technologie, um Ihnen die Geheimnisse des Schicksals zu enthüllen und die Richtung Ihres Lebens zu weisen.", "features": {"aiReading": "🔮 KI-gestützte Interpretation", "instant": "⚡ Sofortige Wahrsagerei", "accurate": "🌟 <PERSON><PERSON><PERSON><PERSON><PERSON>"}, "startButton": "✨ <PERSON><PERSON><PERSON>", "stats": {"users": "<PERSON><PERSON><PERSON> ve<PERSON>", "readings": "Anzahl der Wahrsagungen", "accuracy": "Genauigkeit"}}, "features": {"title": "Warum uns wählen", "description": "Wir kombinieren traditionelle Tarot-Weisheit mit modernster KI-Technologie, um Ihnen das präziseste und angenehmste Wahrsageerlebnis zu bieten.", "items": {"aiReading": {"title": "KI-gestützte Interpretation", "description": "In Kombination mit Algorithmen für maschinelles Lernen, tiefgehende Analyse der Bedeutung von <PERSON>, um genaue personalisierte Interpretationen zu liefern."}, "instant": {"title": "Sofortige Wahrsagerei", "description": "<PERSON><PERSON> er<PERSON>, erhalten Sie sofort Wahrsagerergebnisse und erkunden Sie die Antworten Ihres Herzens jederzeit und überall."}, "accurate": {"title": "Prä<PERSON><PERSON>", "description": "Basierend auf dem <PERSON> von Zehnmillionen Wahrsagedaten beträgt die Genauigkeit bis zu 99 % und ist vertrauenswürdig."}, "personalized": {"title": "Personalisierte Analyse", "description": "<PERSON>ür Ihre Probleme und Ihren Hintergrund bieten wir maßgeschneiderte Wahrsageberatung und Lebensberatung."}, "privacy": {"title": "Datenschutz", "description": "Wir schützen die Privatsphäre der Benutzer strikt. Alle Wahrsageaufzeichnungen sind nur für Si<PERSON> sichtbar, sicher und zuverlässig."}, "crossPlatform": {"title": "Plattformübergreifende Unterstützung", "description": "Unterstützt die Synchronisierung zwischen Mobiltelefonen, Tablets und Computern, sodass Sie jederzeit und überall Wahrsagen können."}}}, "spreads": {"title": "Wahrsage-Kartendecks", "description": "Wählen Sie die Wahrsagemethode, die zu Ihnen passt, und erkunden Sie die Geheimnisse des Schicksals."}, "cta": {"title": "Sind Si<PERSON> bereit, Ihre Wahrsagereise zu beginnen?", "description": "<PERSON><PERSON> sich von den AI-Tarotkarten die Antworten Ihres Herzens offenbaren und Ihnen den Weg weisen.", "startButton": "Beginnen Sie jetzt mit dem Wahrsagen", "historyButton": "<PERSON><PERSON><PERSON><PERSON>"}}, "components": {"ui": {"reset": "Z<PERSON>ücksetzen"}, "tarot": {"cardInfo": {"cardBack": "Kartenrück<PERSON>"}, "drawCard": {"loadingCards": "Kartendaten werden geladen...", "continueDivination": "<PERSON><PERSON> wahr<PERSON>gen", "networkError": "Netzwerk-Anfrage fehlgeschlagen", "streamError": "Antwortstrom kann nicht gelesen werden", "updateError": "Aktualisierung der KI-Interpretationsergebnisse fehlgeschlagen", "interpretationError": "Interpretation fehlgeschlagen"}, "questionSpread": {"title": "Wahrsageinformationen", "spreadName": "Kartenlegen-Name:", "yourQuestion": "<PERSON><PERSON><PERSON>:", "spreadDescription": "Kartenlegen-Beschreibung:", "recommendationReason": "Empfehlungsgrund:", "noInfo": "<PERSON><PERSON> Wahrsageinformationen vorhanden"}, "sessionsTable": {"statusCreated": "<PERSON><PERSON><PERSON><PERSON>", "statusDrawing": "Karten werden gezogen", "statusCompleted": "Abgeschlossen", "headers": {"question": "Wahrsagefrage", "userName": "<PERSON><PERSON><PERSON><PERSON>", "spreadName": "Kartenlegenamen", "spreadCategory": "Kartenlegekategorie", "cardCount": "<PERSON><PERSON><PERSON><PERSON>", "status": "Zustand", "deletedStatus": "Gelöschter Status", "createdAt": "Erstellungszeit", "completedAt": "Fertigstellungszeit", "actions": "Operation"}, "noRecords": "<PERSON><PERSON>rsagungsaufzeichnungen vorhanden", "unknownUser": "<PERSON><PERSON><PERSON><PERSON>", "deleted": "Gelöscht", "normal": "Normal", "continueDivination": "<PERSON><PERSON> wahr<PERSON>gen", "details": "Details"}, "spreadPreview": {"cardCount": "<PERSON><PERSON>"}}}}