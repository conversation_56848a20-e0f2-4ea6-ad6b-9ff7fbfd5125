import { BookOpen } from 'lucide-react'
import { notFound } from 'next/navigation'
import { getTranslations } from 'next-intl/server'

import { getSessionById } from '@/actions/questions'

import RecordDetailClient from './RecordDetailClient'

export default async function RecordPage({ params }: { params: Promise<{ recordId: string }> }) {
  const t = await getTranslations('records')
  // 获取当前记录ID
  const { recordId } = await params

  // 获取该记录详情（假设 getQuestions 支持 id 查询，或需新建 getSessionById）
  // 这里用 getQuestions({ id }) 作为示例
  let session: any = null
  try {
    session = await getSessionById(recordId)
  } catch (e) {
    session = null
    console.error('Error recommending tarot spread:', e)
  }

  if (!session) {
    return notFound()
  }

  return (
    <div className="min-h-screen">
      <div className="container mx-auto max-w-2xl px-2 py-6 md:px-0">
        <div className="mb-6 flex flex-col items-center gap-2 text-center">
          <div className="relative inline-flex items-center justify-center">
            <div className="from-primary/20 absolute inset-0 rounded-full bg-gradient-to-r to-purple-500/20 blur-xl"></div>
            <div className="from-primary/10 relative rounded-full bg-gradient-to-r to-purple-500/10 p-4">
              <BookOpen className="text-primary h-8 w-8" />
            </div>
          </div>
          <h1 className="from-primary via-primary bg-gradient-to-r to-purple-400 bg-clip-text text-3xl font-bold text-transparent md:text-4xl">
            {t('detail.title')}
          </h1>
        </div>

        {/* 问题、牌阵等基本信息 */}
        <div className="mb-6 rounded-xl border border-purple-400/20 bg-black/30 p-4 text-base text-gray-200 shadow-sm">
          <div className="mb-2 font-semibold text-purple-300">{t('detail.question')}</div>
          <div className="mb-4 text-lg">{session.question}</div>
          <div className="flex flex-wrap gap-4 text-sm text-gray-400">
            <div>
              {t('detail.spread')}: {session.spreadName}
            </div>
            <div>
              {t('detail.category')}: {session.spreadCategory}
            </div>
            <div>
              {t('detail.cardCount')}: {session.cardCount}
            </div>
            <div>
              {t('table.status')}：
              {session.status === 'completed'
                ? t('status.completed')
                : session.status === 'drawing'
                  ? t('status.drawing')
                  : t('status.created')}
            </div>
            <div>
              {t('detail.createdAt')}: {formatDate(session.createdAt)}
            </div>
            {session.completedAt && (
              <div>
                {t('detail.completedAt')}: {formatDate(session.completedAt)}
              </div>
            )}
          </div>
        </div>

        {/* AI解读与卡牌展示 */}
        <RecordDetailClient session={{ ...session, cards: parseSessionCards(session) }} />
      </div>
    </div>
  )
}

function formatDate(timestamp: number | Date) {
  const date = typeof timestamp === 'number' ? new Date(timestamp) : timestamp
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

function parseSessionCards(session: any) {
  if (session.cards) {
    try {
      return JSON.parse(session.cards)
    } catch {
      return []
    }
  }
  return []
}
