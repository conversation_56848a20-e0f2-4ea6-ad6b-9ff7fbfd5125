import { Button } from '@/components/ui/button'

const plans = [
  {
    title: '基础方案',
    price: '免费',
    features: ['1000个免费token', '每周一次每日深度运势解析', '1款免费卡背', '3款免费牌阵', '保存最近3天解读结果'],
    highlight: false,
    tag: '',
    button: '立即体验',
    bg: 'bg-black/30',
    border: 'border-purple-400/20'
  },
  {
    title: '连续包月',
    price: 'US$9.9 /每月',
    features: [
      '10000个免费token，每月刷新',
      '无限次每日免费深度运势解析',
      '12套精美专属卡背',
      '解锁所有牌阵',
      '保存所有解读结果',
      '随时可取消订阅'
    ],
    highlight: true,
    tag: '最受欢迎',
    button: '立即订阅',
    bg: 'bg-gradient-to-b from-purple-900/60 to-black/60',
    border: 'border-purple-400'
  },
  {
    title: '连续包年',
    price: 'US$89.9 /每年',
    subPrice: '约$7.49/月',
    features: ['包含连续包月所有权益', '立省25%', '随时可取消订阅'],
    highlight: false,
    tag: '节省25%',
    button: '立即订阅',
    bg: 'bg-black/30',
    border: 'border-purple-400/20'
  }
]

export default function MembershipPage() {
  return (
    <div className="min-h-screen bg-gradient-to-b px-2 py-12">
      <div className="mx-auto max-w-5xl">
        <div className="mb-10 text-center">
          <h2 className="mb-2 text-3xl font-bold tracking-wide text-purple-100 md:text-4xl">会员订阅</h2>
          <div className="text-base text-purple-300 md:text-lg">解锁全部高级功能，享受更多专属权益</div>
        </div>
        <div className="flex flex-col items-stretch justify-center gap-8 md:flex-row">
          {plans.map((plan, idx) => (
            <div
              key={plan.title}
              className={`relative flex-1 rounded-2xl border p-8 shadow-xl ${plan.border} ${plan.bg} flex flex-col ${plan.highlight ? 'z-10 scale-105 ring-2 ring-purple-400' : ''}`}
            >
              {plan.tag && (
                <div
                  className={`absolute -top-4 right-6 rounded-full px-3 py-1 text-xs font-semibold ${plan.highlight ? 'bg-purple-600 text-white' : 'bg-purple-800 text-purple-200'}`}
                >
                  {plan.tag}
                </div>
              )}
              <div className="mb-4 text-lg font-semibold tracking-wide text-purple-200">{plan.title}</div>
              <div className="mb-2 text-3xl font-extrabold text-white">{plan.price}</div>
              {plan.subPrice && <div className="mb-2 text-sm text-purple-300">{plan.subPrice}</div>}
              <ul className="mt-4 mb-8 space-y-2 text-sm text-purple-100">
                {plan.features.map((f) => (
                  <li key={f} className="flex items-center gap-2">
                    <span className="text-green-400">✓</span>
                    <span>{f}</span>
                  </li>
                ))}
              </ul>
              <Button
                className={`mt-auto w-full py-3 text-lg font-medium ${plan.highlight ? 'bg-purple-600 hover:bg-purple-700' : 'bg-purple-800 hover:bg-purple-900'} text-white`}
              >
                {plan.button}
              </Button>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}
