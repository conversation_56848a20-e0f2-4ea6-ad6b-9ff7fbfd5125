'use client'

import BlogBody from '@/components/blog/blog-body'
import MysticalCard from '@/components/tarot/MysticalCard'
import { Card, CardContent } from '@/components/ui/card'

interface DailyCardProps {
  card: CardType | null
  interpretation?: string | null
  onClick: () => void
}

export default function DailyCard({ card, interpretation, onClick }: DailyCardProps) {
  // 检查卡牌是否为逆位
  const isReversed = card && ((card as any).direction === 'reversed' || (card as any).position === 'reversed')

  return (
    <div className="space-y-4">
      <div className="relative">
        {/* 塔罗牌图片 */}
        <div className="w-full py-8">
          <MysticalCard width="320px" height="530px" card={card} onClick={onClick} isReversed={!!isReversed} />
        </div>

        {/* 卡牌信息 */}
        {card && (
          <div className="mt-4 space-y-2 text-center">
            <h3 className="text-xl font-bold text-white">{card.name}</h3>
            <p className="text-purple-200">
              {card.name} {isReversed ? '(逆位)' : '(正位)'}
            </p>
          </div>
        )}
      </div>

      {/* 运势解读 */}
      <Card className="border-purple-400/20 bg-black/40 text-white">
        <CardContent className="p-4">
          <h3 className="mb-3 text-lg font-semibold text-purple-200">今日运势解读</h3>
          {interpretation && <BlogBody content={interpretation} />}
        </CardContent>
      </Card>
    </div>
  )
}
