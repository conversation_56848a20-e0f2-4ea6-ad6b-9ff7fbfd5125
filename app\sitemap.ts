import { unstable_noStore } from 'next/cache'

import { locales } from '@/i18n/routing'

import type { MetadataRoute } from 'next'

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  unstable_noStore()

  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL

  const routes = ['/']

  const entries: MetadataRoute.Sitemap = []

  for (const route of routes) {
    for (const locale of locales) {
      entries.push({
        url: `${baseUrl}${locale.code === 'en' ? '' : `/${locale.code}`}${route}`
      })
    }
  }

  return [...entries]
}
