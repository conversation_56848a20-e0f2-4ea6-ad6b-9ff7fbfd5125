'use client'

import {
  LogIn,
  LogOut,
  User,
  History,
  BarChart3,
  Calendar,
  Crown,
  Settings,
  MessageSquare,
  CreditCard
} from 'lucide-react'
import { signOut, useSession } from 'next-auth/react'
import { useTranslations } from 'next-intl'
import { useEffect, useState } from 'react'

import LoginForm from '@/components/login/login-form'
import ChinaLogin from '@/components/login/login-form-cn'
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar'
import { Button } from '@/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from '@/components/ui/dialog'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu'
import { Sheet, SheetContent, SheetHeader, Sheet<PERSON>it<PERSON>, SheetTrigger } from '@/components/ui/sheet'
import { useIsMobile } from '@/hooks/use-mobile'
import { useRouter } from '@/i18n/navigation'
import { isChina } from '@/lib/isChina'
// 图标映射
const iconMap = {
  User: User,
  History: History,
  BarChart3: BarChart3,
  Calendar: Calendar,
  Crown: Crown,
  Settings: Settings,
  MessageSquare: MessageSquare,
  CreditCard: CreditCard
}

export default function LoginModal() {
  const [isOpen, setIsOpen] = useState(false)
  const [inChina, setInChina] = useState(false)
  const session = useSession()
  const t = useTranslations('login')
  const site = useTranslations('siteInfo')
  const isMobile = useIsMobile()
  const router = useRouter()
  const navLinks = []

  useEffect(() => {
    isChina().then((isChina) => {
      if (isChina) {
        setInChina(true)
      }
    })
  }, [])

  if (session.data?.user) {
    // navLinks.push({ href: '/profile', label: '个人中心', icon: 'User' })
    navLinks.push({ href: '/feedback', label: '意见反馈', icon: 'MessageSquare' })
  }

  if (session.status === 'loading') return null

  if (session.status === 'authenticated') {
    if (isMobile) {
      return (
        <Sheet>
          <SheetTrigger asChild>
            <Button variant="ghost" size="icon" className="h-8 w-8 rounded-full p-0">
              <Avatar className="h-8 w-8">
                <AvatarImage src={session.data?.user?.image || ''} alt={session.data?.user?.name || 'User'} />
                <AvatarFallback>
                  {session.data?.user?.name?.[0] || session.data?.user?.email?.[0] || '?'}
                </AvatarFallback>
              </Avatar>
            </Button>
          </SheetTrigger>
          <SheetContent side="left" className="w-[300px] bg-gray-900 text-white">
            <SheetHeader className="mb-6">
              <SheetTitle className="text-white">用户菜单</SheetTitle>
            </SheetHeader>

            <div className="space-y-2">
              {navLinks.map((el) => {
                const IconComponent = iconMap[el.icon as keyof typeof iconMap]
                return (
                  <Button
                    key={el.href}
                    variant="ghost"
                    className="w-full justify-start text-white hover:bg-gray-800"
                    onClick={() => router.push(el.href)}
                  >
                    {IconComponent && <IconComponent className="mr-3 h-5 w-5" />}
                    {el.label}
                  </Button>
                )
              })}

              <div className="border-t border-gray-700 pt-2">
                <Button
                  variant="ghost"
                  className="w-full justify-start text-red-400 hover:bg-red-900/20"
                  onClick={() => signOut()}
                >
                  <LogOut className="mr-3 h-5 w-5" />
                  退出登入
                </Button>
              </div>
            </div>
          </SheetContent>
        </Sheet>
      )
    }

    return (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" size="icon" className="h-8 w-8 rounded-full p-0">
            <Avatar className="h-8 w-8">
              <AvatarImage src={session.data?.user?.image || ''} alt={session.data?.user?.name || 'User'} />
              <AvatarFallback>{session.data?.user?.name?.[0] || session.data?.user?.email?.[0] || '?'}</AvatarFallback>
            </Avatar>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-56">
          <DropdownMenuLabel>
            <div className="flex flex-col space-y-1">
              {session.data?.user?.name && <p className="font-medium">{session.data.user.name}</p>}
              {session.data?.user?.email && (
                <p className="text-muted-foreground truncate text-xs">{session.data.user.email}</p>
              )}
            </div>
          </DropdownMenuLabel>
          <DropdownMenuSeparator />

          {navLinks.map((el) => {
            const IconComponent = iconMap[el.icon as keyof typeof iconMap]
            return (
              <DropdownMenuItem key={el.href} onClick={() => router.push(el.href)}>
                {IconComponent && <IconComponent className="mr-2 h-4 w-4" />}
                <span>{el.label}</span>
              </DropdownMenuItem>
            )
          })}

          <DropdownMenuSeparator />

          <DropdownMenuItem
            className="text-destructive focus:text-destructive cursor-pointer"
            onClick={() => signOut()}
          >
            <LogOut className="mr-2 h-4 w-4" />
            <span>{t('signOut')}</span>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    )
  }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <div className="text-foreground hover:text-primary group relative flex cursor-pointer items-center font-medium transition-colors">
          <LogIn className="mr-2 h-4 w-4" />
          {t('login')}
          <span className="bg-primary absolute -bottom-1 left-0 h-0.5 w-0 transition-all duration-300 group-hover:w-full"></span>
        </div>
      </DialogTrigger>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="text-center text-2xl font-bold">{site('brandName')}</DialogTitle>
          <DialogDescription className="text-center">{t('modal.description')}</DialogDescription>
        </DialogHeader>
        {inChina ? <LoginForm onSuccess={() => setIsOpen(false)} /> : <ChinaLogin onSuccess={() => setIsOpen(false)} />}
      </DialogContent>
    </Dialog>
  )
}
