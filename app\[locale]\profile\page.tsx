'use client'

import { User, Activity, Crown } from 'lucide-react'
import { useSession } from 'next-auth/react'
import { useTranslations } from 'next-intl'
import { useEffect, useState } from 'react'

import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'

interface TokenUsage {
  used: number
  total: number
  remaining: number
}

interface PersonalInfo {
  birthday?: string
  gender?: 'male' | 'female' | 'other'
}

interface ProfileData {
  tokenUsage: TokenUsage
  personalInfo: PersonalInfo
}

export default function ProfilePage() {
  const { data: session } = useSession()
  const t = useTranslations('profile')
  const [profileData, setProfileData] = useState<ProfileData | null>(null)
  const [loading, setLoading] = useState(true)
  const [editingPersonalInfo, setEditingPersonalInfo] = useState(false)
  const [personalInfoForm, setPersonalInfoForm] = useState<PersonalInfo>({})

  useEffect(() => {
    if (session?.user) {
      fetchProfileData()
    }
  }, [session])

  const fetchProfileData = async () => {
    try {
      const response = await fetch('/api/profile')
      if (response.ok) {
        const data = (await response.json()) as ProfileData
        setProfileData(data)
        setPersonalInfoForm(data.personalInfo)
      }
    } catch (error) {
      console.error('Error fetching profile data:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleUpdatePersonalInfo = async () => {
    try {
      const response = await fetch('/api/profile', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(personalInfoForm)
      })

      if (response.ok) {
        const data = (await response.json()) as { personalInfo: PersonalInfo }
        setProfileData((prev) =>
          prev
            ? {
                ...prev,
                personalInfo: data.personalInfo
              }
            : null
        )
        setEditingPersonalInfo(false)
      }
    } catch (error) {
      console.error('Error updating personal info:', error)
    }
  }

  if (!session) {
    return (
      <div className="container mx-auto py-8">
        <Card>
          <CardContent className="pt-6">
            <p className="text-muted-foreground text-center">{t('loginRequired')}</p>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="container mx-auto py-8">
        <Card>
          <CardContent className="pt-6">
            <p className="text-muted-foreground text-center">{t('loading')}</p>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="min-h-screen">
      <div className="container mx-auto space-y-8 py-8">
        {/* 页面标题 - 塔罗牌风格 */}
        <div className="space-y-4 text-center">
          <div className="relative inline-flex items-center justify-center">
            <div className="from-primary/20 via-primary/30 to-primary/20 absolute inset-0 rounded-full bg-gradient-to-r blur-xl"></div>
            <div className="from-primary/80 to-primary relative rounded-full bg-gradient-to-r p-4">
              <Crown className="text-primary-foreground h-8 w-8" />
            </div>
          </div>
          <h1 className="from-primary via-primary bg-gradient-to-r to-purple-400 bg-clip-text text-4xl font-bold text-transparent">
            {t('title')}
          </h1>
          <p className="text-muted-foreground mx-auto max-w-md">{t('description')}</p>
        </div>

        <div className="grid gap-6 md:grid-cols-3">
          {/* Token 使用情况 - 神秘风格 */}
          <Card className="border-primary/20 from-card to-card/50 bg-gradient-to-br backdrop-blur-sm md:col-span-2">
            <CardHeader>
              <CardTitle className="flex items-center gap-3 text-lg">
                <div className="from-primary/80 to-primary rounded-lg bg-gradient-to-r p-2">
                  <Activity className="text-primary-foreground h-5 w-5" />
                </div>
                <span className="from-foreground to-foreground/80 bg-gradient-to-r bg-clip-text text-transparent">
                  {t('tokenUsage.title')}
                </span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-6 sm:grid-cols-3">
                <div className="group relative">
                  <div className="from-primary/30 absolute inset-0 rounded-xl bg-gradient-to-r to-purple-600/30 blur-md transition-all duration-300 group-hover:blur-lg"></div>
                  <div className="from-primary/10 border-primary/20 relative rounded-xl border bg-gradient-to-br to-transparent p-6 text-center backdrop-blur-sm">
                    <div className="from-primary mb-2 bg-gradient-to-r to-purple-400 bg-clip-text text-3xl font-bold text-transparent">
                      {profileData?.tokenUsage.remaining === undefined
                        ? t('tokenUsage.unlimited')
                        : profileData?.tokenUsage.remaining}
                    </div>
                    <div className="text-muted-foreground text-sm font-medium">{t('tokenUsage.remaining')}</div>
                  </div>
                </div>
                <div className="group relative">
                  <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-orange-500/20 to-red-500/20 blur-md transition-all duration-300 group-hover:blur-lg"></div>
                  <div className="relative rounded-xl border border-orange-500/20 bg-gradient-to-br from-orange-500/5 to-transparent p-6 text-center backdrop-blur-sm">
                    <div className="mb-2 bg-gradient-to-r from-orange-400 to-red-400 bg-clip-text text-3xl font-bold text-transparent">
                      {profileData?.tokenUsage.used || 0}
                    </div>
                    <div className="text-muted-foreground text-sm font-medium">{t('tokenUsage.used')}</div>
                  </div>
                </div>
                <div className="group relative">
                  <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-blue-500/20 to-cyan-500/20 blur-md transition-all duration-300 group-hover:blur-lg"></div>
                  <div className="relative rounded-xl border border-blue-500/20 bg-gradient-to-br from-blue-500/5 to-transparent p-6 text-center backdrop-blur-sm">
                    <div className="mb-2 bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-3xl font-bold text-transparent">
                      {profileData?.tokenUsage.total === 0 ? t('tokenUsage.unlimited') : profileData?.tokenUsage.total}
                    </div>
                    <div className="text-muted-foreground text-sm font-medium">{t('tokenUsage.total')}</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 个人信息 - 神秘风格 */}
          <Card className="to-card border-purple-500/20 bg-gradient-to-br from-purple-900/10 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="flex items-center gap-3 text-lg">
                <div className="rounded-lg bg-gradient-to-r from-purple-600/80 to-purple-500 p-2">
                  <User className="h-5 w-5 text-white" />
                </div>
                <span className="bg-gradient-to-r from-purple-300 to-purple-100 bg-clip-text text-transparent">
                  {t('personalInfo.title')}
                </span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {!editingPersonalInfo ? (
                <>
                  <div>
                    <Label className="text-sm font-medium">{t('personalInfo.birthday')}</Label>
                    <p className="text-muted-foreground text-sm">
                      {profileData?.personalInfo.birthday || t('personalInfo.notSet')}
                    </p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium">{t('personalInfo.gender')}</Label>
                    <p className="text-muted-foreground text-sm">
                      {profileData?.personalInfo.gender
                        ? t(`personalInfo.${profileData.personalInfo.gender}`)
                        : t('personalInfo.notSet')}
                    </p>
                  </div>
                  <Button variant="outline" size="sm" onClick={() => setEditingPersonalInfo(true)} className="w-full">
                    {t('personalInfo.edit')}
                  </Button>
                </>
              ) : (
                <>
                  <div className="space-y-2">
                    <Label htmlFor="birthday">{t('personalInfo.birthday')}</Label>
                    <Input
                      id="birthday"
                      type="date"
                      value={personalInfoForm.birthday || ''}
                      onChange={(e) =>
                        setPersonalInfoForm((prev) => ({
                          ...prev,
                          birthday: e.target.value
                        }))
                      }
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="gender">{t('personalInfo.gender')}</Label>
                    <Select
                      value={personalInfoForm.gender || ''}
                      onValueChange={(value) =>
                        setPersonalInfoForm((prev) => ({
                          ...prev,
                          gender: value as 'male' | 'female' | 'other'
                        }))
                      }
                    >
                      <SelectTrigger>
                        <SelectValue placeholder={t('personalInfo.genderPlaceholder')} />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="male">{t('personalInfo.male')}</SelectItem>
                        <SelectItem value="female">{t('personalInfo.female')}</SelectItem>
                        <SelectItem value="other">{t('personalInfo.other')}</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="flex gap-2">
                    <Button size="sm" onClick={handleUpdatePersonalInfo} className="flex-1">
                      {t('personalInfo.save')}
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        setEditingPersonalInfo(false)
                        setPersonalInfoForm(profileData?.personalInfo || {})
                      }}
                      className="flex-1"
                    >
                      {t('personalInfo.cancel')}
                    </Button>
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
