import { useState, useEffect } from 'react'

import useElementSize from '@/hooks/use-element-size'
import { getCardPositionConfig, type ScaleType, type CardArrType } from '@/lib/card-pos'
import { useCards } from '@/lib/load-cards'

const useInit = (slug: string[], sessionData: TarotSession, locale: string = 'en') => {
  const { width: containerWidth, height: containerHeight } = useElementSize('card_container')
  const { cards, loading: cardsLoading } = useCards(locale)

  const [cardArr, setCardArr] = useState<CardArrType[]>([])
  const [scale, setScale] = useState<ScaleType>({ x: 0, y: 0 })
  const [cardInfos, setCardInfos] = useState<CurrentCardType[]>([])
  const [curCard, setCurCard] = useState<CurrentCardType | null>(null)
  const [showInfo, setShowInfo] = useState(false)

  useEffect(() => {
    if (!cardsLoading && cards.length > 0) {
      // 如果容器尺寸还是0，尝试延迟一下再计算
      if (containerWidth === 0 || containerHeight === 0) {
        const timer = setTimeout(() => {
          // 尝试重新获取容器尺寸
          const element = document.getElementById('card_container')
          if (element && element.offsetWidth > 0 && element.offsetHeight > 0) {
            const config = getCardPositionConfig(slug, element.offsetWidth, element.offsetHeight)
            setScale(config.scale)
            setCardArr(config.cardArr)
          }
        }, 100)
        return () => clearTimeout(timer)
      } else {
        const config = getCardPositionConfig(slug, containerWidth, containerHeight)
        setScale(config.scale)
        setCardArr(config.cardArr)
      }
    }
  }, [slug, containerWidth, containerHeight, cardsLoading, cards.length])

  useEffect(() => {
    if (!cardsLoading && cards.length > 0 && cardArr.length > 0) {
      if (sessionData && sessionData.cards && sessionData.cards.length > 0) {
        const arr = JSON.parse(sessionData.cards)
        const infos = arr.map((item: CurrentCardType) => ({
          ...cards[item.index],
          position: item.position,
          flipped: item.flipped,
          direction: item.direction,
          index: item.index
        }))
        setCardInfos(infos)
      } else {
        const tempIndexes = Array.from({ length: cards.length }, (_, index) => index)
        const newIndexes: number[] = []
        for (let i = 0; i < cardArr.length; i++) {
          // 防止在卡牌数量多于总牌数时出错
          if (tempIndexes.length === 0) break
          const rand = Math.floor(Math.random() * tempIndexes.length)
          newIndexes.push(tempIndexes.splice(rand, 1)[0])
        }
        const infos = newIndexes.map((cardI, idx) => ({
          ...cards[cardI],
          position: idx + 1,
          flipped: false,
          direction: Math.random() > 0.5 ? 'reversed' : ('normal' as 'normal' | 'reversed'),
          index: cardI
        }))
        setCardInfos(infos)
      }
    }
  }, [sessionData, cardArr.length, cardsLoading, cards])

  // function onReload() {
  //   setflipStates(new Array(cardNum).fill(false))
  //   setInfoShown(new Array(cardNum).fill(false))
  //   // shuffleIndexes(cards)
  // }

  function onCardClick(index: number) {
    if (!cardInfos[index].flipped) {
      // 翻牌
      const newArr = cardInfos.map((el, idx) => (idx === index ? { ...el, flipped: true } : el))
      setCardInfos(newArr)
      // 即时保存抽牌结果到数据库，避免用户刷新页面后重复翻牌
      fetch('/api/tarot/session', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          sessionId: sessionData.id,
          cards: newArr.map((item) => ({
            position: item.position,
            flipped: item.flipped,
            direction: item.direction,
            index: item.index
          }))
        })
      }).catch((error) => console.error('保存抽牌结果失败:', error))
    } else {
      // 查看卡牌详情
      setCurCard(cardInfos[index])
      setShowInfo(true)
    }
  }

  function closeInfo() {
    setShowInfo(false)
  }

  return {
    onCardClick,
    closeInfo,
    cards,
    cardArr,
    scale,
    curCard,
    showInfo,
    cardInfos,
    cardsLoading
  }
}

export default useInit
