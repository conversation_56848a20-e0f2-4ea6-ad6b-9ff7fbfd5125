# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules


messages/*.d.json.ts

# testing
/coverage

.open-next

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env
.env*.local
.env.production

# vercel
.vercel
.wrangler

# typescript
*.tsbuildinfo
next-env.d.ts

.idea
