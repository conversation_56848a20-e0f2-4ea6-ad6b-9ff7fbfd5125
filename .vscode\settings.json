{"typescript.tsdk": "node_modules/typescript/lib", "editor.codeActionsOnSave": {"source.fixAll.eslint": "always"}, "editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "files.associations": {"*.css": "tailwindcss"}, "tailwindCSS.includeLanguages": {"typescript": "javascript", "typescriptreact": "javascript"}, "tailwindCSS.experimental.classRegex": [["cva\\(([^)]*)\\)", "[\"'`]([^\"'`]*).*?[\"'`]"], ["cn\\(([^)]*)\\)", "[\"'`]([^\"'`]*).*?[\"'`]"]], "typescript.preferences.importModuleSpecifier": "non-relative", "javascript.preferences.importModuleSpecifier": "non-relative", "editor.quickSuggestions": {"strings": true}, "cSpell.words": ["<PERSON><PERSON>", "supabase", "<PERSON><PERSON><PERSON><PERSON>"], "i18n-ally.localesPaths": ["messages", "i18n"], "i18n-ally.keystyle": "nested", "i18n-ally.displayLanguage": "zh", "i18n-ally.sourceLanguage": "en"}