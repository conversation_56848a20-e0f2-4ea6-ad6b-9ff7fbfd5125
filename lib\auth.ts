import { Dr<PERSON><PERSON>Adapter } from '@auth/drizzle-adapter'
import { eq } from 'drizzle-orm'
import NextAuth from 'next-auth'
import Google from 'next-auth/providers/google'
import ResendProvider from 'next-auth/providers/resend'

import { createDb } from '@/lib/db'

import { accounts, sessions, users, verificationTokens, userUsage } from './db/schema'

export const { handlers, signIn, signOut, auth } = NextAuth(() => {
  const db = createDb()

  return {
    secret: process.env.AUTH_SECRET,
    adapter: DrizzleAdapter(db, {
      usersTable: users,
      accountsTable: accounts,
      sessionsTable: sessions,
      verificationTokensTable: verificationTokens
    }),
    providers: [
      Google,
      ResendProvider({
        from: '<EMAIL>'
      })
    ],
    session: {
      strategy: 'jwt'
    },
    callbacks: {
      jwt: async ({ token, user }) => {
        if (user) {
          token.id = user.id
        }
        return token
      },
      session: async ({ session, token }) => {
        if (token && session.user) {
          session.user.id = token.id as string
        }
        return session
      }
    },
    events: {
      createUser: async ({ user }) => {
        try {
          await db.insert(userUsage).values({
            userId: user.id!,
            usedTokens: 0,
            totalTokens: 2000
          })
          // 新增：创建用户时设置默认牌背
          await db
            .update(users)
            .set({ cardBackUrl: 'https://static.destinyai.tools/tarot/card-backs/card_bgm.png' })
            .where(eq(users.id, user.id!))
        } catch (error) {
          console.error('Failed to create userUsage:', error)
        }
      }
    }
  }
})
