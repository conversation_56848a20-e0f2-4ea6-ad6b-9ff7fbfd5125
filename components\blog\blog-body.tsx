import Markdown from 'markdown-to-jsx'

import Heading from '@/components/markdown/heading'
import ImageBlock from '@/components/markdown/img'
import PBlock from '@/components/markdown/p'
import VideoBlock from '@/components/markdown/video'

interface BlogBodyProps {
  content: string
}

const BlogBody = ({ content }: BlogBodyProps) => {
  return (
    <Markdown
      options={{
        overrides: {
          p: ({ children }) => <PBlock>{children}</PBlock>,
          h1: ({ children }) => <Heading level={1}>{children}</Heading>,
          h2: ({ children }) => <Heading level={2}>{children}</Heading>,
          h3: ({ children }) => <Heading level={3}>{children}</Heading>,
          h4: ({ children }) => <Heading level={4}>{children}</Heading>,
          h5: ({ children }) => <Heading level={5}>{children}</Heading>,
          h6: ({ children }) => <Heading level={6}>{children}</Heading>,
          pre: ({ children }) => <>{children}</>,
          img: ({ src, alt }) => <ImageBlock src={src} alt={alt} />,
          CustomVideo: ({ src, title }) => <VideoBlock src={src} title={title} />
        }
      }}
    >
      {content}
    </Markdown>
  )
}

export default BlogBody
