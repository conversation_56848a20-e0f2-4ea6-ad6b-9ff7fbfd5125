'use server'

import { GoogleGenAI } from '@google/genai'

import { hasEnoughTokens, updateUserTokenUsage } from '@/actions/token-management'
import { auth } from '@/lib/auth'
import { getLanguageNameFromLocale } from '@/lib/utils'

interface SpreadRecommendation {
  spreadType: string
  spreadName: string
  reason: string
  spreadCategory: string
  spreadDesc: string
  spreadGuide?: string
  spreadLink?: string
  cardCount: number
}
// 推荐牌阵
export async function recommendTarotSpread(question: string, locale = 'en'): Promise<SpreadRecommendation> {
  const session = await auth()
  if (!session?.user?.id) {
    throw new Error('Unauthorized')
  }

  const hasTokens = await hasEnoughTokens(session.user.id, 0)
  // console.log(hasTokens)
  if (!hasTokens) {
    throw new Error('Not enough tokens')
  }

  try {
    const languageName = getLanguageNameFromLocale(locale)

    const ai = new GoogleGenAI({
      apiKey: process.env.GEMINI_API_KEY
    })

    const systemPrompt = `
You are a professional tarot reader and AI assistant. Based on the user's question, recommend the most suitable tarot spread from the given spread data and provide detailed reasoning.

Please respond in ${languageName} language (locale: ${locale}).

## Available Spread Types:

### 1. Daily Readings (daily) - For general questions and comprehensive guidance
   (1) Single Card (daily/single) - 1 card - Draw one tarot card for divination on any question
   (2) Linear Spread (daily/linear) - 3 cards - Good for interpreting event sequences, cause-effect relationships
   (3) Foundation Spread (daily/foundation) - 3 cards - Advisory spread to understand current situation and influences
   (4) Balanced Spread (daily/balanced) - 3 cards - Three cards with a common focal point, each aspect equally important
   (5) Celtic Cross (daily/celtic-cross) - 10 cards - Famous 10-card spread for comprehensive answers
   (6) Either Or (daily/either-or) - 5 cards - When torn between two options, see the key aspects of both
   (7) New Year Planning (daily/new-years-planning) - 8 cards - Summarize the past year and prepare for the new one

### 2. Career Development (career) - For work, career, and professional planning questions
   (1) Self Alignment (career/self-alignment) - 9 cards - Understand role and purpose for business/project foundation
   (2) Job Search (career/job-search) - 6 cards - Reveal internal and external obstacles to career potential
   (3) Business Strategy (career/business-strategy) - 10 cards - Show all aspects and provide problem-solving strategies
   (4) Brick by Brick (career/brick-by-brick) - 6 cards - Examine work situation and find long-term development picture
   (5) Facing Challenges (career/facing-challenges) - 6 cards - When work is difficult, diagnose problems and find solutions
   (6) Mind and Heart (career/mind-and-heart) - 6 cards - Examine emotional and practical life, see how career affects these
   (7) Shooting Forward (career/shooting-forward) - 5 cards - Find paths and inner qualities to achieve ideal career

### 3. Love & Relationships (love-relationship) - For love, interpersonal relationships, emotional concerns
   (1) Classic 3-Card (love-relationship/3-card) - 3 cards - Quick diagnosis of relationship dynamics between two people
   (2) Cross Spread (love-relationship/5-card-cross) - 5 cards - Detailed explanation of how relationship develops over time
   (3) Finding Love (love-relationship/finding-love) - 5 cards - Explore what future love will look like
   (4) Compatibility (love-relationship/compatibility) - 7 cards - Focus on similarities, differences, and compatibility
   (5) Readiness for Love (love-relationship/readiness-for-love) - 6 cards - Understand your readiness for love
   (6) Stay or Go (love-relationship/stay-or-go) - 6 cards - Guidance when relationship has serious problems
   (7) Broken Heart (love-relationship/broken-heart) - 6 cards - Examine relationship status, reveal repair efforts

### 4. Spiritual Exploration (spiritual) - For self-knowledge, spiritual growth, inner exploration
   (1) Self Growth (spiritual/self-growth) - 10 cards - Balance inner desires and others' expectations, see true choices
   (2) Dream Mirror (spiritual/dream-mirror) - 7 cards - Compare dream world and waking world, understand dream messages
   (3) Jungian Archetypes (spiritual/jungian-archetypes) - 5 cards - Find your qualities in different Jungian archetypes
   (4) Self Love (spiritual/self-love) - 6 cards - Highlight your best and most lovable parts, learn to love yourself

## Recommendation Strategy:
- Analyze the core theme and emotional background of the user's question
- Consider the complexity of the question (simple questions recommend fewer cards, complex questions recommend more cards)
- Match the most relevant spread type and specific spread
- Prioritize spreads that can deeply analyze the essence of the question

## Return Format:
Strictly return in the following JSON format, do not add any markdown markers:

{
  "spreadType": "spiritual",
  "spreadCategory": "Spiritual Exploration", 
  "spreadName": "Self Growth",
  "spreadDesc": "Balance inner desires and others' expectations, see true choices",
  "spreadLink": "spiritual/self-growth",
  "cardCount": 10,
  "reason": "Detailed recommendation reason in ${languageName}"
}

## Important Constraints:
1. spreadType can only be: daily, career, love-relationship, spiritual
2. spreadName, spreadLink, cardCount must come from the actual existing spreads above
3. Do not fabricate or modify any data
4. cardCount must match the actual number of cards in the selected spread
5. Translate spreadCategory and spreadName to ${languageName}
6. Write the reason in ${languageName}

## Recommendation Reason Requirements:
- Concise and clear, approximately 50-80 words
- Directly relate to the user's question content
- Explain how this spread helps solve the user's confusion
- Warm, professional tone with wisdom
- Reflect professional tarot reader expertise and deep understanding
- Write in ${languageName} language
`

    const userPrompt = `The user's question is: ${question}`

    const model = 'gemini-2.0-flash-exp'

    const chat = ai.chats.create({
      model: model,
      config: {
        maxOutputTokens: 8192,
        temperature: 0.7,
        systemInstruction: {
          parts: [{ text: systemPrompt }]
        }
      }
    })

    const result = await chat.sendMessage({
      message: [{ text: userPrompt }]
    })

    let tokensUsed = 0

    if (result.usageMetadata) {
      const inputTokens = result.usageMetadata.promptTokenCount || 0
      const outputTokens = result.usageMetadata.candidatesTokenCount || 0
      tokensUsed = inputTokens + outputTokens
    }

    await updateUserTokenUsage(session.user.id, tokensUsed)

    const response = result.text!

    // 清理响应文本，移除可能的markdown标记
    let cleanResponse = response.trim()
    if (cleanResponse.startsWith('```json')) {
      cleanResponse = cleanResponse.replace(/^```json\s*/, '').replace(/\s*```$/, '')
    }
    if (cleanResponse.startsWith('```')) {
      cleanResponse = cleanResponse.replace(/^```\s*/, '').replace(/\s*```$/, '')
    }

    const recommendation = JSON.parse(cleanResponse) as SpreadRecommendation

    return recommendation
  } catch (error) {
    console.error('Error recommending tarot spread:', error)

    // 根据语言返回默认推荐
    const getDefaultRecommendation = (locale: string) => {
      const isZh = locale === 'zh'
      return {
        spreadType: 'daily',
        spreadName: isZh ? '单张' : 'Single Card',
        reason: isZh
          ? '基础牌阵是最适合解答各类问题的万能牌阵，能为你提供全面的视角和实用的建议。'
          : 'The foundation spread is the most versatile array for answering all types of questions, providing you with comprehensive perspective and practical advice.',
        spreadCategory: isZh ? '常规占卜' : 'Daily Readings',
        spreadDesc: isZh
          ? '抽取一张塔罗牌，可以对任何问题做出占卜'
          : 'Draw one tarot card for divination on any question',
        spreadLink: 'daily/single',
        cardCount: 1
      }
    }

    return getDefaultRecommendation(locale)
  }
}
// 每日运势解答
export async function dailyInterpretation(form: any, locale = 'en') {
  const session = await auth()
  if (!session?.user?.id) {
    throw new Error('Unauthorized')
  }

  const hasTokens = await hasEnoughTokens(session.user.id, 0)
  if (!hasTokens) {
    throw new Error('Not enough tokens')
  }

  try {
    const db = (await import('@/lib/db')).createDb()
    const userId = session.user.id

    // 获取用户生日和性别
    const [userInfo] = await db
      .select()
      .from((await import('@/lib/db/schema')).userUsage)
      .where((await import('drizzle-orm')).eq((await import('@/lib/db/schema')).userUsage.userId, userId))
      .limit(1)
    const birthday = userInfo?.birthday
    const gender = userInfo?.gender

    // 卡牌信息
    const card = form.spreads?.[0] || form.card || {}
    const cardName = card.name || card.nameEn || '未知牌'
    const cardPosition = card.position || card.direction || 'upright'
    const isReversed = cardPosition === 'reversed'

    const languageName = getLanguageNameFromLocale(locale)

    const ai = new GoogleGenAI({
      apiKey: process.env.GEMINI_API_KEY
    })

    // 构建用户信息字符串
    let userInfoStr = ''

    if (birthday) {
      userInfoStr += `Birthday: ${birthday}`
    }
    if (gender) {
      userInfoStr += userInfoStr ? `,Gender:${gender}` : `Gender: ${gender}`
    }

    const systemPrompt = `
You are a professional tarot reader and AI assistant, skilled in interpreting the deep meanings of tarot cards and providing accurate, warm, and insightful daily fortune readings combined with users' personal information.

Please respond in ${languageName} language (locale: ${locale}).

## Reading Principles:
1. **Personalized Interpretation**: Combine user's birthday and gender information to provide readings that better match personal characteristics
2. **Upright vs Reversed Differences**: Accurately distinguish between upright and reversed meanings, where reversed typically indicates obstacles, internalization, or opposite energy
3. **Practical Guidance**: Provide specific actionable advice to help users apply tarot wisdom in their daily lives
4. **Positive Orientation**: Even with challenging cards, find opportunities for growth and learning
5. **Keyword Emphasis**: Use **bold** formatting for important concepts, emotions, and action recommendations

## Reading Structure:
Please output in the following markdown format, ensuring clear structure:

### 🔮 Today's Tarot Card
**${cardName}** ${isReversed ? '(Reversed)' : '(Upright)'}

### ✨ Card Interpretation
[Detailed explanation of this card's core meaning in today's fortune, combining specific meanings of upright/reversed positions]

### 🌟 Personalized Guidance
[Combine user's birthday and gender characteristics to provide personalized interpretation and advice]

### 💫 Today's Key Focus Areas
- **Emotional Aspect**: [Emotional guidance]
- **Action Recommendations**: [Specific action guidance]
- **Points to Note**: [Areas that need attention]

### 🎯 Today's Lucky Tip
[Provide a brief and practical lucky suggestion or positive energy reminder]

## Reading Requirements:
- Keep total word count between 300-500 words
- Use warm, professional language with a sense of wisdom
- Avoid overly abstract expressions, provide specific actionable advice
- Use **bold** formatting to emphasize key concepts
- Combine traditional tarot meanings with modern life applications
- If user information is incomplete, still provide valuable general readings
- Ensure all content is written in ${languageName} language
`

    const noInfoText = 'No personal information provided'

    const userPrompt = `
Please provide today's tarot fortune reading for the following information:

**Drawn Tarot Card**: ${cardName} ${isReversed ? '(Reversed)' : '(Upright)'}
**User Information**: ${userInfoStr || noInfoText}
**Reading Date**: ${new Date().toLocaleDateString()}

Please provide today's fortune reading and life guidance based on this tarot card's meaning combined with the user's personal information. Remember to respond in ${languageName} language.
`

    const model = 'gemini-2.0-flash-exp'

    const chat = ai.chats.create({
      model: model,
      config: {
        maxOutputTokens: 8192,
        temperature: 0.7,
        systemInstruction: {
          parts: [{ text: systemPrompt }]
        }
      }
    })

    const result = await chat.sendMessage({
      message: [{ text: userPrompt }]
    })

    let tokensUsed = 0

    if (result.usageMetadata) {
      const inputTokens = result.usageMetadata.promptTokenCount || 0
      const outputTokens = result.usageMetadata.candidatesTokenCount || 0
      tokensUsed = inputTokens + outputTokens
    }

    await updateUserTokenUsage(session.user.id, tokensUsed)

    const interpretation = result.text!

    return interpretation
  } catch (error) {
    console.error('Error generating daily interpretation:', error)

    // 错误时返回基础解读
    const card = form.spreads?.[0] || form.card || {}
    const cardName = card.name || card.nameEn || '未知牌'
    const cardPosition = card.position || card.direction || 'upright'
    const isReversed = cardPosition === 'reversed'

    // 根据语言返回默认解读
    const getDefaultInterpretation = (locale: string, cardName: string, isReversed: boolean) => {
      const isZh = locale === 'zh'

      if (isZh) {
        return `### 🔮 今日塔罗牌
**${cardName}** ${isReversed ? '（逆位）' : '（正位）'}

### ✨ 牌面解读
今日抽到的塔罗牌为您带来了特殊的能量和指引。这张牌提醒您关注内心的声音，相信自己的直觉。

### 💫 今日重点关注
- **保持开放的心态**，接受新的可能性
- **相信自己的判断力**，做出明智的决定
- **关注内心的平衡**，保持身心和谐

### 🎯 今日幸运提示
相信宇宙的安排，每一天都是新的开始。保持积极的心态，好运自然会来到您身边。`
      } else {
        return `### 🔮 Today's Tarot Card
**${cardName}** ${isReversed ? '(Reversed)' : '(Upright)'}

### ✨ Card Interpretation
Today's tarot card brings you special energy and guidance. This card reminds you to listen to your inner voice and trust your intuition.

### 💫 Today's Key Focus Areas
- **Keep an open mind** and accept new possibilities
- **Trust your judgment** and make wise decisions
- **Focus on inner balance** and maintain harmony of body and mind

### 🎯 Today's Lucky Tip
Trust the universe's arrangement, every day is a new beginning. Maintain a positive attitude, and good luck will naturally come to you.`
      }
    }

    return getDefaultInterpretation(locale, cardName, isReversed)
  }
}
