import { sql } from 'drizzle-orm'
import { integer, primaryKey, sqliteTable, text } from 'drizzle-orm/sqlite-core'

import type { AdapterAccountType } from 'next-auth/adapters'

export const users = sqliteTable('user', {
  id: text('id')
    .primaryKey()
    .$defaultFn(() => crypto.randomUUID()),
  name: text('name'),
  email: text('email').unique(),
  emailVerified: integer('emailVerified', { mode: 'timestamp_ms' }),
  image: text('image'),
  cardBackUrl: text('cardBackUrl') // 用户自定义牌背图片URL
})
export const accounts = sqliteTable(
  'account',
  {
    userId: text('userId')
      .notNull()
      .references(() => users.id, { onDelete: 'cascade' }),
    type: text('type').$type<AdapterAccountType>().notNull(),
    provider: text('provider').notNull(),
    providerAccountId: text('providerAccountId').notNull(),
    refresh_token: text('refresh_token'),
    access_token: text('access_token'),
    expires_at: integer('expires_at'),
    token_type: text('token_type'),
    scope: text('scope'),
    id_token: text('id_token'),
    session_state: text('session_state')
  },
  (account) => ({
    compoundKey: primaryKey({
      columns: [account.provider, account.providerAccountId]
    })
  })
)

export const sessions = sqliteTable('session', {
  sessionToken: text('sessionToken').primaryKey(),
  userId: text('userId')
    .notNull()
    .references(() => users.id, { onDelete: 'cascade' }),
  expires: integer('expires', { mode: 'timestamp_ms' }).notNull()
})

export const verificationTokens = sqliteTable(
  'verificationToken',
  {
    identifier: text('identifier').notNull(),
    token: text('token').notNull(),
    expires: integer('expires', { mode: 'timestamp_ms' }).notNull()
  },
  (verificationToken) => ({
    compositePk: primaryKey({
      columns: [verificationToken.identifier, verificationToken.token]
    })
  })
)
// 用户token消耗表
export const userUsage = sqliteTable('userUsage', {
  id: text('id')
    .primaryKey()
    .$defaultFn(() => crypto.randomUUID()),
  userId: text('userId')
    .notNull()
    .references(() => users.id, { onDelete: 'cascade' }),
  usedTokens: integer('usedTokens').notNull().default(0),
  totalTokens: integer('totalTokens').notNull().default(0),
  // 用户个人信息
  birthday: text('birthday'), // 存储为 YYYY-MM-DD 格式
  gender: text('gender').$type<'male' | 'female' | 'other'>() // 性别：男性、女性、其他
})

export type TarotSessionStatus = 'created' | 'drawing' | 'completed'

// 塔罗牌占卜记录表
export const tarotSessions = sqliteTable('tarot_sessions', {
  id: text('id')
    .primaryKey()
    .$defaultFn(() => crypto.randomUUID()),
  userId: text('user_id').references(() => users.id, { onDelete: 'set null' }),
  // 占卜问题信息
  question: text('question').notNull(),
  spreadName: text('spread_name').notNull(),
  spreadCategory: text('spread_category').notNull(),
  spreadDesc: text('spread_desc'),
  reason: text('reason'), // AI推荐理由
  cardCount: integer('card_count').notNull(),
  spreadLink: text('spread_link'),
  // 抽牌结果（JSON格式存储）
  cards: text('cards'), // 存储抽中的卡牌信息
  // AI解读结果
  aiInterpretation: text('ai_interpretation'),
  // 占卜状态
  status: text('status').$type<TarotSessionStatus>().default('created').notNull(),
  // 软删除标识 (0: 未删除, 1: 已删除)
  isDeleted: integer('is_deleted').default(0).notNull(),
  // 时间戳
  createdAt: integer('created_at', { mode: 'timestamp_ms' })
    .default(sql`CURRENT_TIMESTAMP`)
    .notNull(),
  updatedAt: integer('updated_at', { mode: 'timestamp_ms' })
    .default(sql`CURRENT_TIMESTAMP`)
    .notNull(),
  completedAt: integer('completed_at', { mode: 'timestamp_ms' })
})

// 每日运势表
export const dailyFortune = sqliteTable('daily_fortune', {
  id: integer('id').primaryKey({ autoIncrement: true }),
  userId: text('user_id')
    .notNull()
    .references(() => users.id, { onDelete: 'cascade' }),
  card: text('card').notNull(), // JSON字符串
  interpretation: text('interpretation').notNull(),
  timestamp: integer('timestamp').notNull()
})
