{"name": "taotreading", "version": "0.1.0", "private": true, "engines": {"node": ">=20.0.0", "pnpm": ">=9.0.0 <10.0.0"}, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "format": "prettier --write .", "preview": "opennextjs-cloudflare build && opennextjs-cloudflare preview", "deploy": "opennextjs-cloudflare build && opennextjs-cloudflare deploy", "upload": "opennextjs-cloudflare build && opennextjs-cloudflare upload", "cf-typegen": "wrangler types --env-interface CloudflareEnv cloudflare-env.d.ts", "deploy:c": "tsx scripts/deploy/index.ts", "db:generate": "drizzle-kit generate", "db:generate:empty": "drizzle-kit generate --custom --name", "db:migrate-local": "tsx scripts/migrate.ts local", "db:migrate-remote": "tsx scripts/migrate.ts remote", "db:seed": "wrangler dev scripts/seed.ts", "db:studio:local": "tsx scripts/db-studio-local.ts", "db:studio:remote": "drizzle-kit studio", "i18n:translate": "tsx scripts/18n/cli.ts --mode=missing", "i18n:delete": "tsx scripts/18n/cli.ts --d", "i18n:keys": "tsx scripts/18n/cli.ts --mode=keys --keys", "i18n:list": "tsx scripts/18n/cli.ts --list-locales", "i18n:sequential": "tsx scripts/18n/sequential-cli.ts"}, "dependencies": {"@auth/drizzle-adapter": "^1.9.0", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@google/genai": "^1.4.0", "@opennextjs/cloudflare": "^1.2.1", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.9", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-switch": "^1.2.4", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "axios": "^1.10.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "date-fns": "^4.1.0", "drizzle-orm": "^0.43.1", "lucide-react": "^0.487.0", "markdown-to-jsx": "^7.7.4", "next": "15.3.1", "next-auth": "5.0.0-beta.25", "next-intl": "^4.1.0", "react": "19.1.0", "react-dom": "19.1.0", "sonner": "^2.0.3", "swiper": "^11.2.8", "tailwind-merge": "^3.2.0", "tw-animate-css": "^1.2.9"}, "devDependencies": {"@cloudflare/workers-types": "^4.20250409.0", "@shikijs/markdown-it": "^3.2.1", "@tailwindcss/postcss": "^4.1.5", "@tailwindcss/typography": "^0.5.16", "@types/node": "^20", "@types/react": "19.1.0", "@types/react-dom": "19.1.1", "@types/react-syntax-highlighter": "^15.5.13", "@types/yargs": "^17.0.33", "better-sqlite3": "^11.9.1", "cloudflare": "^4.2.0", "dotenv": "^16.5.0", "drizzle-kit": "^0.31.0", "eslint": "^8", "eslint-config-next": "^15.1.3", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-unused-imports": "^4.1.4", "postcss": "^8", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^4.1.5", "tsx": "^4.19.3", "typescript": "^5", "wrangler": "^4.10.0", "yargs": "^17.7.2"}}