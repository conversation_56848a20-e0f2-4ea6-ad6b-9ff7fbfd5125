'use client'

import { Sparkles, Info } from 'lucide-react'
// import { useSearchParams } from 'next/navigation'
import { useSession } from 'next-auth/react'
import { useTranslations, useLocale } from 'next-intl'
import { useState, useMemo } from 'react'
import { toast } from 'sonner'

import { Button } from '@/components/ui/button'
import useInit from '@/hooks/use-init'
// import { useRouter } from '@/i18n/navigation'

import Card from './Card'
import CardInfoDialog from './CardInfoDialog'
import InterpretationDialog from './InterpretationDialog'
import QuestionSpreadDialog from './QuestionSpreadDialog'

export default function DrawCard({
  slug,
  sessionData,
  onSessionUpdate
}: {
  slug: string[]
  sessionData: TarotSession
  onSessionUpdate?: (updatedSession: TarotSession) => void
}) {
  // const searchParams = useSearchParams()
  const t = useTranslations('draw')
  const t2 = useTranslations('components.tarot.drawCard')
  const locale = useLocale()
  // const router = useRouter()
  const session = useSession()

  const safeCardBackUrl =
    (session.data?.user as any)?.userCardBackUrl || 'https://static.destinyai.tools/tarot/card-bgm.png'

  // 传入当前阶段，让useInit决定是否执行初始化逻辑
  const { onCardClick, closeInfo, cardInfos, cardArr, scale, curCard, showInfo, cardsLoading } = useInit(
    slug,
    sessionData,
    locale
  )

  // 弹窗状态管理
  const [isQuestionSpreadDialogOpen, setQuestionSpreadDialogOpen] = useState(false)

  // AI 解读相关状态
  const [isInterpretationOpen, setInterpretationOpen] = useState(false)
  const [interpretationText, setInterpretationText] = useState('')
  const [isInterpreting, setIsInterpreting] = useState(false)

  // 检查是否所有牌都已翻开
  const allFlipped = useMemo(() => {
    if (cardInfos.length) {
      return cardInfos.every((card) => card.flipped)
    }
    return false
  }, [cardInfos])

  // 检查是否已有AI解读结果
  const hasInterpretation = useMemo(() => {
    return sessionData?.aiInterpretation && sessionData.aiInterpretation.length > 0
  }, [sessionData?.aiInterpretation])

  // 获取按钮文本
  const getButtonText = () => {
    if (hasInterpretation) {
      return `✨ ${t('viewResult')}`
    }
    if (allFlipped) {
      return `✨ ${t('title')}`
    }
    return t('turnAll')
  }

  // AI解读函数
  async function getInterpretation() {
    // 如果已经解读过，直接展示以往的解读结果，不再调用api
    if (sessionData?.aiInterpretation) {
      setInterpretationText(sessionData.aiInterpretation)
      setInterpretationOpen(true)
      return
    }

    setInterpretationText('')
    setIsInterpreting(true)
    setInterpretationOpen(true)

    try {
      const response = await fetch('/api/tarot/interpret', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          question: sessionData?.question,
          spreadName: sessionData?.spreadName,
          spreads: cardInfos,
          spreadDesc: sessionData?.spreadDesc,
          locale: locale
        })
      })

      if (!response.ok) {
        const errorData = (await response.json().catch(() => ({}))) as { error?: string }

        // 检查是否是 token 不足的错误
        if (errorData.error === 'Not enough tokens') {
          toast.error('Token 不足', {
            description: '您的 AI 使用额度已用完，请稍后再试或联系客服',
            duration: 5000,
            position: 'top-center'
          })
          setIsInterpreting(false)
          setInterpretationOpen(false)
          return
        }

        throw new Error('网络请求失败')
      }

      const reader = response.body?.getReader()
      if (!reader) {
        throw new Error('无法读取响应流')
      }

      const decoder = new TextDecoder()
      let accumulated = ''

      while (true) {
        const { done, value } = await reader.read()

        if (done) break

        const chunk = decoder.decode(value, { stream: true })
        const lines = chunk.split('\n')

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6)

            if (data === '[DONE]') {
              setIsInterpreting(false)

              // 更新会话中的AI解读结果
              if (accumulated) {
                try {
                  const updateResponse = await fetch('/api/tarot/session', {
                    method: 'PUT',
                    headers: {
                      'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                      sessionId: sessionData.id,
                      aiInterpretation: accumulated,
                      status: 'completed'
                    })
                  })

                  if (updateResponse.ok) {
                    // 更新本地sessionData状态，避免重复调用AI
                    const updatedSession = {
                      ...sessionData,
                      aiInterpretation: accumulated,
                      status: 'completed' as const,
                      completedAt: Date.now()
                    }

                    // 通知父组件更新sessionData
                    if (onSessionUpdate) {
                      onSessionUpdate(updatedSession)
                    }
                  } else {
                    console.error('更新AI解读结果失败: HTTP', updateResponse.status)
                  }
                } catch (error) {
                  console.error('更新AI解读结果失败:', error)
                }
              }

              return
            }

            try {
              const parsed = JSON.parse(data)
              if (parsed.text) {
                accumulated += parsed.text
                setInterpretationText(accumulated)
              } else if (parsed.error) {
                throw new Error(parsed.error)
              }
            } catch (e) {
              // 忽略JSON解析错误，继续处理下一行
              console.error('Error parsing JSON:', e)
            }
          }
        }
      }

      setIsInterpreting(false)
    } catch (error) {
      console.error('解读失败:', error)

      // 检查是否是 token 不足的错误
      if (error instanceof Error && error.message.includes('Not enough tokens')) {
        toast.error('Token 不足', {
          description: '您的 AI 使用额度已用完，请稍后再试或联系客服',
          duration: 5000,
          position: 'top-center'
        })
      } else {
        toast.error('解读失败', {
          description: '请稍后重试或联系客服',
          duration: 4000
        })
      }

      setInterpretationText(t('error'))
      setIsInterpreting(false)
    }
  }

  // 处理AI解读按钮点击
  const handleAIInterpretation = () => {
    getInterpretation()
  }

  // 处理AI解读弹窗关闭，跳转回塔罗牌首页
  const handleInterpretationClose = () => {
    setInterpretationOpen(false)
    // router.replace('/')
  }

  // 如果卡牌数据还在加载中，显示加载状态
  if (cardsLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="mx-auto max-w-6xl space-y-8">
          <div className="flex min-h-[80vh] items-center justify-center">
            <div className="text-center">
              <div className="mx-auto mb-4 h-12 w-12 animate-spin rounded-full border-4 border-purple-200 border-t-purple-600"></div>
              <p className="text-muted-foreground text-lg">{t2('loadingCards')}</p>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <>
      <div className="container mx-auto px-4 py-8">
        <div className="mx-auto max-w-6xl space-y-8">
          {/* 顶部按钮区域 */}
          <div className="flex justify-center gap-4">
            <Button
              onClick={() => setQuestionSpreadDialogOpen(true)}
              className="flex items-center gap-2 bg-gradient-to-r from-purple-600 to-pink-600 text-white shadow-lg transition-all duration-300 hover:from-purple-700 hover:to-pink-700 hover:shadow-xl"
              size="lg"
            >
              <Info className="h-5 w-5" />
              {t('dialogTitle')}
            </Button>

            <Button
              onClick={handleAIInterpretation}
              disabled={!hasInterpretation && !allFlipped}
              className="flex items-center gap-2 bg-gradient-to-r from-amber-500 to-orange-500 text-white shadow-lg transition-all duration-300 hover:from-amber-600 hover:to-orange-600 hover:shadow-xl disabled:cursor-not-allowed disabled:from-slate-600 disabled:to-slate-600 disabled:opacity-50"
              size="lg"
            >
              <Sparkles className="h-5 w-5" />
              {getButtonText()}
            </Button>
          </div>

          {/* 翻牌区域 */}
          <div className="relative min-h-[80vh] w-full" id="card_container">
            {cardArr.map(
              (item, index) =>
                cardInfos[index] && (
                  <div key={index} className="absolute h-0 w-0" style={{ top: item.top, left: item.left }}>
                    <Card
                      rotate={item.rotate}
                      scale={scale}
                      curCard={cardInfos[index]}
                      onClick={() => onCardClick(index)}
                      userCardBackUrl={safeCardBackUrl}
                    />
                  </div>
                )
            )}
          </div>
        </div>
      </div>
      {/* 卡牌信息 */}
      {curCard && <CardInfoDialog curCard={curCard} showInfo={showInfo} closeInfo={closeInfo} />}
      {/* 问题和牌阵信息弹窗 */}
      <QuestionSpreadDialog
        isOpen={isQuestionSpreadDialogOpen}
        onClose={() => setQuestionSpreadDialogOpen(false)}
        question={sessionData?.question}
        spreadName={sessionData?.spreadName}
        spreadCategory={sessionData?.spreadCategory}
        spreadDesc={sessionData?.spreadDesc || ''}
        reason={sessionData?.reason || ''}
      />

      {/* AI解读弹窗 */}
      <InterpretationDialog
        isOpen={isInterpretationOpen}
        onClose={handleInterpretationClose}
        text={interpretationText}
        isLoading={isInterpreting}
        cards={cardInfos}
      />
    </>
  )
}
