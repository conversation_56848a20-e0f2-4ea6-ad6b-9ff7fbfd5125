{"headers": {"blogs": "Блог", "home": "Главная страница", "about": "О", "submitTools": "Инструмент отправки", "navigation": "Навигация по сайту", "navigationDescription": "Посетите основные разделы нашего сайта"}, "siteInfo": {"brandName": "Destiny AI", "meta": {"title": "Destiny AI - Инструмент для гадания на картах Таро с использованием ИИ", "description": "Откройте для себя лучшие инструменты гадания на основе искусственного интеллекта, такие как карты Таро, астрология, И Цзин и т. д. Ваше руководство по пониманию современных техник гадания."}}, "divinationCategories": {"tarot": {"name": "Гадание на картах Таро", "description": "Метод гадания, использующий колоду из 78 карт с символическими изображениями для получения информации о прошлом, настоящем и будущем.", "origin": "Италия/Франция (Европа XV века)", "seoTitle": "Гадание на картах Таро с помощью ИИ - Бесплатные онлайн-значения карт и прогнозы", "seoDescription": "Получите точное онлайн-гадание на картах Таро с помощью ИИ. Исследуйте любовную жизнь, карьерные пути и будущее, раскрываемые картами, с помощью нашего передового инструмента прогнозирования карт Таро."}, "astrology": {"name": "Астрология", "description": "Изучение того, как положение и движение небесных тел влияют на человеческие дела и природные явления.", "origin": "Множественные источники (Древн<PERSON> Вавилон, Ег<PERSON><PERSON><PERSON><PERSON>, Греция)", "seoTitle": "Астрологические карты и гороскопы AI — Персонализированные интерпретации знаков зодиака", "seoDescription": "Исследуйте свою космическую судьбу с помощью астрологических инструментов на базе искусственного интеллекта. Получите персональные натальные карты, ежедневные гороскопы и интерпретации совместимости в зависимости от вашего знака зодиака."}, "iChing": {"name": "И-Цзин", "description": "Древняя китайская система гадания, в которой используются гексаграммы для предоставления рекомендаций и предсказания будущих событий.", "origin": "Китай (ок. 1000–750 гг. до н. э.)", "seoTitle": "Гадание по И-Цзин с помощью ИИ — Толкования И-Цзин и гексаграммы", "seoDescription": "Обратитесь к древней Книге Перемен с помощью нашего гадания по И-Цзин с использованием ИИ. Получите персональные интерпретации гексаграмм, разъяснения и рекомендации для важных жизненных решений."}, "numerology": {"name": "Нумерология", "description": "Вера в священную или мистическую связь между числами и событиями, раскрывающая информацию о личности и будущем посредством вычислений.", "origin": "Множественные источники (Древняя Греция, Еги<PERSON>е<PERSON>, Вавилон)", "seoTitle": "Калькулятор нумерологии AI - Анализ жизненного пути и чисел судьбы", "seoDescription": "Используйте наш инструмент нумерологии AI для расчета своего жизненного пути, судьбы и чисел выражения. Узнайте, как числа влияют на вашу личность, отношения и будущее."}, "palmistry": {"name": "Хиромантия", "description": "Идентифицировать людей и предсказывать будущее, изучая линии, формы и текстуры ладони.", "origin": "Индия (около 4000 лет назад)", "seoTitle": "AI-хиромантия — нумерология и анализ рук", "seoDescription": "Загрузите фотографию для анализа хиромантии с помощью ИИ. Узнайте, как линии вашей руки раскрывают вашу личную жизнь, профессиональный успех, здоровье и будущее с помощью наших цифровых инструментов для хиромантии."}, "dreamInterpretation": {"name": "Толкование снов", "description": "Анализируйте сны, чтобы раскрыть скрытые смыслы, информацию и прогнозы на будущее.", "origin": "Древ<PERSON>ий Египет, Греция и Месопотамия", "seoTitle": "AI-анализ сновидений — значение снов и символов", "seoDescription": "Поймите свои сны с помощью AI-анализа сновидений. Получите персональные интерпретации символов сновидений, повторяющихся тем и подсознательной информации во сне."}, "vedic": {"name": "Ведическая астрология", "description": "Древняя индийская астрологическая система, основанная на положении звезд, а не на регрессионных позициях, предлагающая подробный анализ судьбы, характера и жизненных событий.", "origin": "Индия (около 1500 г. до н.э.)", "seoTitle": "AI Ведическая астрология - Дж<PERSON>о<PERSON>иш натальная карта и прогнозы", "seoDescription": "Откройте для себя свой истинный космический план с помощью ведической астрологии AI. Получите точные интерпретации Джйотиш, натальную карту и прогнозы для карьеры, отношений и жизненного пути."}, "categoryNotFound": "Категория не найдена", "categoryNotFoundDescription": "Категория гадания, которую вы ищете, не найдена. Пожалуйста, посетите нашу домашнюю страницу, чтобы увидеть доступные категории.", "other": {"name": "Другое", "description": "Различные альтернативные техники гадания и мистические практики, не охватываемые традиционными категориями.", "origin": "Различные культуры по всему миру", "seoTitle": "Альтернативные инструменты для гадания с использованием ИИ - Уникальные методы предсказания судьбы", "seoDescription": "Исследуйте нетрадиционные методы гадания с использованием ИИ и уникальные техники предсказания судьбы. Откройте для себя редкие мистические практики и альтернативные пути понимания судьбы."}, "comprehensive": {"name": "Комплексный", "description": "Платформа, предлагающая несколько методов гадания и интегрированные методы предсказания судьбы в одном месте.", "origin": "Современная интеграция традиционных практик", "seoTitle": "Интегрированная платформа для гадания с использованием ИИ - Различные методы предсказания судьбы", "seoDescription": "Получите доступ к множеству методов гадания с использованием ИИ в одном месте. Комплексная платформа предлагает Таро, астрологию, нумерологию и многое другое, предоставляя всестороннее духовное руководство и услуги по предсказанию судьбы."}}, "footer": {"description": "Destiny AI - ваш комплексный инструмент для гадания на картах Таро с использованием искусственного интеллекта.", "contact": {"title": "Свяжитесь с нами", "intro": "Если у вас есть какие-либо вопросы, пожалуйста, свяжитесь с нами:", "email": "<EMAIL>"}, "quickLinks": {"title": "Быстрые ссылки", "home": "Главная страница", "aboutUs": "О нас", "refundPolicy": "Политика возврата средств", "subscriptionTerms": "Условия подписки"}, "categories": "Классификация", "copyright": "© 2025 Destiny AI Tools Все права защищены."}, "login": {"signOut": "Выйти", "login": "Войти", "modal": {"description": "Войдите, чтобы продолжить ваше путешествие", "triggerText": "Войти"}, "form": {"continueWithGoogle": "Продолжить с Google", "continueWithGithub": "Продолжить с GitHub", "orContinueWith": "Или продолжить", "emailPlaceholder": "Введите свой адрес электронной почты", "signInWithEmail": "Войти с помощью электронной почты"}}, "common": {"loading": "Загрузка...", "cancel": "Отмена", "close": "Закрыть", "previous": "Предыдущая страница", "next": "Следующая страница", "save": "Сохранить", "edit": "Редактировать", "delete": "Удалить", "submit": "Отправить", "morePages": "Показать больше страниц", "language": "Язык", "selectLanguage": "Выберите язык", "loadingSpread": "Загрузка данных расклада..."}, "tarot": {"title": "AI толкование Таро", "description": "Исследуйте неизвестное, получите руководство и откройте внутреннюю мудрость.", "start": "Начните гадание на картах Таро", "startAi": "Начните гадание на картах Таро с использованием ИИ", "desc": "Введение", "upright": "Прямое положение", "reversed": "Перевернутое положение"}, "question": {"title": "Выберите расклад карт Таро", "description": "Введите вопрос, который вы хотите предсказать, и ИИ порекомендует вам наиболее подходящий расклад.", "placeholder": "Пожалуйста, опишите проблему, которую вы хотите предсказать, например: как моя карьера может развиваться в будущем, чтобы добиться успеха...?", "isLogin": "Начните путешествие предсказаний после входа в систему", "isRecommending": "Идет анализ проблемы...", "start": "Введите вопрос, чтобы получить расклад карт, рекомендованный ИИ", "dialogTitle": "Интеллектуальная рекомендация AI", "recommendation": "Рекомендуемый расклад карт", "startDraw": "Начать вытягивать карты", "reSelectSpread": "Выберите другие расклады карт самостоятельно", "selectSpread": "Выберите этот расклад", "login": "Войти", "loginDescription": "Пожалуйста, войдите, чтобы продолжить использование функции гадания.", "spreadInfo": {"title": "Интерпретация расклада", "positionMeaning": "Значение позиций", "meaning": "Значение", "understood": "Я понимаю"}}, "draw": {"dialogTitle": "Информация о гадании", "title": "Персонализированная интерпретация ИИ", "viewResult": "Посмотреть результаты интерпретации", "loading": "Создается интерпретация, пожалуйста, подождите...", "error": "К сожалению, произошла ошибка при интерпретации, повторите попытку позже.", "turnAll": "Пожалуйста, переверните все карты", "selectCard": "Необходимо выбрать {count} карт", "selected": "Выбрано: {current} / {total}", "leftRight": "Просмотр карт влево и вправо", "complete": "Выбор карт завершен! Переход к этапу переворота карт...", "shuffle": "Перемешать", "shuffleDesc": "Пожалуйста, нажмите и перетащите для перемешивания"}, "interpretation": {"title": "AI толкование Таро", "loadingMessage": "ИИ расшифровывает для вас тайны карт Таро...", "loadingSubtitle": "Пожалуйста, подождите, мудрость собирается", "yourQuestion": "Ваш вопрос:", "drawnCards": "Вытянутые карты:", "spreadReading": "Интерпретация расклада карт", "aiDepthReading": "Глубокий анализ ИИ • Личный эксклюзив", "generatingContent": "Содержание интерпретации генерируется...", "flipAllCards": "Пожалуйста, переверните все карты, а затем нажмите кнопку интерпретации."}, "interpretationKeywordsLegend": {"legendTitle": "Легенда ключевых слов", "cards": "Карты Таро", "emotions": "Эмоция", "career": "Карьера", "time": "Время", "states": "Состояние", "advice": "Предложение", "emphasis": "Подчеркивание"}, "profile": {"title": "Личный кабинет", "loginRequired": "Пожалуйста, сначала войдите в систему, чтобы просмотреть личный кабинет", "loading": "Загрузка...", "description": "Исследуйте свою цифровую судьбу, отслеживайте свое таинственное путешествие", "tokenUsage": {"title": "Использование токенов", "remaining": "Остаток токенов", "used": "Использованные токены", "total": "Всего токенов", "unlimited": "Неограниченно"}, "personalInfo": {"title": "Личная информация", "birthday": "День рождения", "gender": "Пол", "male": "Мужской", "female": "Женский", "other": "Другое", "notSet": "Не установлено", "edit": "Редактировать", "save": "Сохранить", "cancel": "Отмена", "birthdayPlaceholder": "Пожалуйста, выберите дату рождения", "genderPlaceholder": "Пожалуйста, выберите пол"}, "tarotHistory": {"title": "Запись гадания", "noRecords": "Нет записей гадания", "question": "Вопрос", "spread": "<PERSON>ас<PERSON><PERSON><PERSON><PERSON> карт", "status": "Состояние", "date": "Время", "viewDetails": "Посмотреть детали", "statusCreated": "Создано", "statusDrawing": "Вытягиваются карты", "statusCompleted": "Завершено", "loadMore": "Загрузить еще"}, "settings": {"title": "Настройки аккаунта", "logout": "Выйти из системы"}}, "records": {"title": "Запись гадания", "description": "Отслеживайте свое таинственное путешествие и вспоминайте каждое указание судьбы.", "totalRecords": "Всего {count} записей", "noRecords": "Нет записей гадания", "startJourney": "Начните свое первое путешествие по чтению Таро", "table": {"question": "Вопрос гадания", "spread": "Название расклада карт", "category": "Категория расклада карт", "cardCount": "Количество карт", "status": "Состояние", "createdAt": "Время создания", "completedAt": "Время завершения", "actions": "Операция", "viewDetails": "Подробности"}, "status": {"completed": "Завершено", "drawing": "Вытягиваются карты", "created": "Создано", "unknown": "Неизвестно"}, "subtitle": "Отслеживайте свое таинственное путешествие и вспоминайте каждое указание судьбы.", "startFirstReading": "Начните свое первое путешествие по чтению Таро", "startDivination": "Начать гадание", "loading": "Загрузка...", "viewDetails": "Посмотреть детали", "view": "Посмотреть", "delete": "Удалить", "deleteConfirm": "Вы уверены, что хотите удалить эту запись?", "deleteSuccess": "Удалено успешно", "cards": "Карта", "showing": "Показать", "recordsText": "Записи", "detail": {"title": "占卜记录详情", "question": "占卜问题：", "spread": "牌阵：", "category": "类别：", "cardCount": "卡牌数量：", "status": "状态：", "createdAt": "创建时间：", "completedAt": "完成时间："}, "page": {"continueDivination": "Продолжить гадание", "deleteError": "Не удалось удалить, попробуйте еще раз", "deleteConfirm": "Вы уверены, что хотите удалить эту запись?"}}, "home": {"hero": {"title": "AI Таро", "subtitle": "Гадалка", "description": "Сочетание древней мудрости и современных технологий искусственного интеллекта, чтобы раскрыть вам тайны судьбы и направить ход вашей жизни.", "features": {"aiReading": "🔮 Интеллектуальная интерпретация AI", "instant": "⚡ Моментальное гадание", "accurate": "🌟 Точное предсказание"}, "startButton": "✨ Начать гадание", "stats": {"users": "Доверие пользователей", "readings": "Количество гаданий", "accuracy": "Точность"}}, "features": {"title": "Почему выбирают нас", "description": "Мы сочетаем традиционную мудрость Таро с передовыми технологиями искусственного интеллекта, чтобы предоставить вам самый точный и внимательный опыт гадания.", "items": {"aiReading": {"title": "Интеллектуальная интерпретация AI", "description": "Сочетание алгоритмов машинного обучения, глубокий анализ значений карт Таро, предоставление точных персональных интерпретаций."}, "instant": {"title": "Мгновенное гадание", "description": "Не нужно ждать, получите результаты гадания мгновенно и исследуйте ответы своего сердца в любое время и в любом месте."}, "accurate": {"title": "Точный прогноз", "description": "На основе обучения десяткам миллионов данных гадания, точность достигает 99%, заслуживает доверия."}, "personalized": {"title": "Персонализированный анализ", "description": "Для ваших проблем и фона мы предлагаем индивидуальные консультации по гаданию и жизненные советы."}, "privacy": {"title": "Защита конфиденциальности", "description": "Мы строго защищаем конфиденциальность пользователей. Все записи гаданий видны только вам, безопасно и надежно."}, "crossPlatform": {"title": "Кроссплатформенная поддержка", "description": "Поддерживает синхронизацию между мобильными телефонами, планшетами и компьютерами, чтобы вы могли гадать в любое время и в любом месте."}}}, "spreads": {"title": "Расклады карт для гадания", "description": "Выберите подходящий вам метод гадания и исследуйте тайны судьбы."}, "cta": {"title": "Вы готовы начать свое путешествие в гадание?", "description": "Позвольте картам Таро с искусственным интеллектом раскрыть ответы вашего сердца и направить вас в правильном направлении.", "startButton": "Начните гадать прямо сейчас", "historyButton": "Просмотреть историю"}}, "components": {"ui": {"reset": "Сбросить"}, "tarot": {"cardInfo": {"cardBack": "Рубашка карты"}, "drawCard": {"loadingCards": "Загрузка данных карты...", "continueDivination": "Продолжить гадание", "networkError": "Сетевой запрос не удался", "streamError": "Невозможно прочитать поток ответа", "updateError": "Не удалось обновить результаты интерпретации ИИ", "interpretationError": "Ошибка интерпретации"}, "questionSpread": {"title": "Информация о гадании", "spreadName": "Название расклада:", "yourQuestion": "Ваш вопрос:", "spreadDescription": "Описание расклада:", "recommendationReason": "Причина рекомендации:", "noInfo": "Нет информации о гадании"}, "sessionsTable": {"statusCreated": "Создано", "statusDrawing": "Вытягиваются карты", "statusCompleted": "Завершено", "headers": {"question": "Вопрос гадания", "userName": "Имя пользователя", "spreadName": "Название расклада карт", "spreadCategory": "Категория расклада карт", "cardCount": "Количество карт", "status": "Состояние", "deletedStatus": "Статус удаления", "createdAt": "Время создания", "completedAt": "Время завершения", "actions": "Операция"}, "noRecords": "Нет записей гадания", "unknownUser": "Неизвестный пользователь", "deleted": "Удалено", "normal": "Нормальный", "continueDivination": "Продолжить гадание", "details": "Подробности"}, "spreadPreview": {"cardCount": "Карта"}}}}