'use client'

import { Send, MessageSquare, Star } from 'lucide-react'
import { useState } from 'react'
import { toast } from 'sonner'

import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Textarea } from '@/components/ui/textarea'

export default function FeedbackPage() {
  const [feedback, setFeedback] = useState('')
  const [rating, setRating] = useState(0)
  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleSubmit = async () => {
    if (!feedback.trim()) {
      toast.error('请输入反馈内容')
      return
    }

    setIsSubmitting(true)
    try {
      // 这里可以添加提交反馈的逻辑
      await new Promise((resolve) => setTimeout(resolve, 1000)) // 模拟提交
      toast.success('感谢您的反馈！')
      setFeedback('')
      setRating(0)
    } catch (error) {
      toast.error('提交失败，请稍后重试')
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className="container mx-auto px-4 py-12">
      <div className="mx-auto max-w-2xl space-y-8">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-white">意见反馈</h1>
          <p className="mt-2 text-gray-300">您的建议是我们进步的动力</p>
        </div>

        <Card className="border-purple-400/20 bg-black/40 text-white backdrop-blur-md">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-purple-400">
              <MessageSquare className="h-5 w-5" />
              分享您的想法
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div>
              <label className="mb-2 block text-sm font-medium">评分</label>
              <div className="flex gap-1">
                {[1, 2, 3, 4, 5].map((star) => (
                  <button
                    key={star}
                    onClick={() => setRating(star)}
                    className="text-2xl transition-colors hover:text-yellow-400"
                  >
                    <Star
                      className={`h-6 w-6 ${star <= rating ? 'fill-yellow-400 text-yellow-400' : 'text-gray-400'}`}
                    />
                  </button>
                ))}
              </div>
            </div>

            <div>
              <label className="mb-2 block text-sm font-medium">反馈内容</label>
              <Textarea
                placeholder="请详细描述您的建议或遇到的问题..."
                value={feedback}
                onChange={(e) => setFeedback(e.target.value)}
                className="min-h-[120px] resize-none border-purple-400/30 bg-black/30 text-white placeholder:text-gray-400 focus:ring-purple-400"
                maxLength={500}
              />
              <div className="mt-1 text-right text-sm text-gray-400">{feedback.length}/500</div>
            </div>

            <Button
              onClick={handleSubmit}
              disabled={isSubmitting || !feedback.trim()}
              className="w-full bg-purple-600 text-white hover:bg-purple-700 disabled:opacity-50"
            >
              {isSubmitting ? (
                <div className="flex items-center gap-2">
                  <div className="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent"></div>
                  提交中...
                </div>
              ) : (
                <div className="flex items-center gap-2">
                  <Send className="h-4 w-4" />
                  提交反馈
                </div>
              )}
            </Button>
          </CardContent>
        </Card>

        <Card className="border-blue-400/20 bg-black/40 text-white backdrop-blur-md">
          <CardHeader>
            <CardTitle className="text-blue-400">联系方式</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm">
              <p>邮箱：<EMAIL></p>
              <p>微信：TarotQA_Support</p>
              <p>QQ群：123456789</p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
