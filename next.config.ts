import { initOpenNextCloudflareForDev } from '@opennextjs/cloudflare'
import { NextConfig } from 'next'
import createNextIntlPlugin from 'next-intl/plugin'

initOpenNextCloudflareForDev()

const nextConfig: NextConfig = {
  devIndicators: false,
  images: {
    remotePatterns: [new URL('https://static.destinyai.tools/**')]
  },
  experimental: {
    staleTimes: {
      dynamic: 3600,
      static: 3600
    }
  }
}

const withNextIntl = createNextIntlPlugin({
  experimental: {
    createMessagesDeclaration: './messages/zh.json'
  }
})
export default withNextIntl(nextConfig)
