import { useTranslations } from 'next-intl'

import MysticalCard from '@/components/tarot/MysticalCard'
import SpreadList from '@/components/tarot/SpreadList'
import { Button } from '@/components/ui/button'
import { Link } from '@/i18n/navigation'

export default function TarotPage() {
  const t = useTranslations('home')

  return (
    <div className="min-h-screen" id="home-container">
      {/* 背景粒子效果 */}
      {/* <div className="pointer-events-none fixed inset-0 overflow-hidden">
        <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_center,_var(--tw-gradient-stops))] from-purple-900/20 via-slate-900/40 to-slate-900"></div>
        {Array.from({ length: 50 }).map((_, i) => (
          <div
            key={i}
            className="animate-float absolute opacity-30"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 5}s`,
              animationDuration: `${3 + Math.random() * 4}s`
            }}
          >
            ✨
          </div>
        ))}
      </div> */}

      {/* 主要内容 */}
      <div className="relative z-10 md:px-20">
        {/* 英雄区域 */}
        <section className="relative flex min-h-screen items-center justify-center overflow-hidden">
          {/* 背景神秘符文 */}
          {/* <div className="absolute inset-0 opacity-5">
            {Array.from({ length: 12 }).map((_, i) => (
              <div
                key={i}
                className="absolute animate-pulse text-6xl text-purple-300"
                style={{
                  left: `${Math.random() * 90 + 5}%`,
                  top: `${Math.random() * 90 + 5}%`,
                  animationDelay: `${i * 0.5}s`,
                  animationDuration: `${3 + Math.random() * 2}s`
                }}
              >
                {['⚡', '🌙', '⭐', '🔮', '✨', '🌟'][Math.floor(Math.random() * 6)]}
              </div>
            ))}
          </div> */}

          <div className="container mx-auto px-4 py-5 md:py-20">
            <div className="relative flex flex-col items-center justify-center gap-16 lg:flex-row">
              {/* 左侧内容 */}
              <div className="flex-1 space-y-8 text-center lg:text-left">
                <div className="mb-4 lg:space-y-4">
                  <h1 className="glow-text text-3xl leading-tight font-bold text-white md:text-5xl lg:text-7xl">
                    <span className="bg-gradient-to-r from-purple-400 via-pink-400 to-purple-400 bg-clip-text text-transparent">
                      {t('hero.title')}
                    </span>
                    <br className="hidden md:block" />
                    <span className="text-white/90">{t('hero.subtitle')}</span>
                  </h1>

                  <div className="my-4 block h-[250px] w-[100%] lg:hidden">
                    <MysticalCard width="160px" height="250px" />
                  </div>

                  <p className="max-w-2xl text-xl leading-relaxed text-gray-300 lg:text-2xl">{t('hero.description')}</p>
                </div>
                {/* 特色标签 */}
                <div className="flex flex-wrap justify-center gap-3 lg:justify-start">
                  {[t('hero.features.aiReading'), t('hero.features.instant'), t('hero.features.accurate')].map(
                    (feature, index) => (
                      <span
                        key={index}
                        className="rounded-full border border-purple-400/30 bg-purple-600/20 px-4 py-2 text-sm text-purple-300 backdrop-blur-sm"
                      >
                        {feature}
                      </span>
                    )
                  )}
                </div>
                {/* 行动按钮 */}
                <div className="flex flex-col justify-center gap-4 sm:flex-row lg:justify-start">
                  <Link href="/question">
                    <Button
                      size="lg"
                      className="group relative overflow-hidden rounded-full bg-gradient-to-r from-purple-600 to-pink-600 px-8 py-4 text-lg transition-all duration-300 hover:scale-105 hover:from-purple-700 hover:to-pink-700 hover:shadow-2xl hover:shadow-purple-500/25"
                    >
                      <span className="relative z-10 flex items-center gap-2">{t('hero.startButton')}</span>
                      <div className="absolute inset-0 bg-gradient-to-r from-pink-600 to-purple-600 opacity-0 transition-opacity duration-300 group-hover:opacity-100"></div>
                    </Button>
                  </Link>
                </div>
                {/* 统计数据 */}
                {/* <div className="grid grid-cols-3 gap-6 pt-8">
                  {[
                    { number: '10,000+', label: t('hero.stats.users') },
                    { number: '50,000+', label: t('hero.stats.readings') },
                    { number: '99%', label: t('hero.stats.accuracy') }
                  ].map((stat, index) => (
                    <div key={index} className="text-center">
                      <div className="mb-1 text-2xl font-bold text-purple-400 lg:text-3xl">{stat.number}</div>
                      <div className="text-sm text-gray-400">{stat.label}</div>
                    </div>
                  ))}
                </div> */}
              </div>
              {/* 右侧3D卡牌 */}
              <div className="hidden min-h-[800px] min-w-[50%] items-center lg:flex">
                <MysticalCard width="320px" height="500px" />
              </div>
            </div>
          </div>
        </section>
        {/* 特色功能区域 */}
        <section className="relative overflow-hidden py-20">
          <div className="container mx-auto px-4">
            <div className="mb-16 text-center">
              <h2 className="mb-6 text-4xl font-bold text-white lg:text-5xl">
                <span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
                  {t('features.title')}
                </span>
              </h2>
              <p className="mx-auto max-w-3xl text-xl leading-relaxed text-gray-300">{t('features.description')}</p>
            </div>
            <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
              {[
                {
                  icon: '🔮',
                  title: t('features.items.aiReading.title'),
                  description: t('features.items.aiReading.description')
                },
                {
                  icon: '⚡',
                  title: t('features.items.instant.title'),
                  description: t('features.items.instant.description')
                },
                {
                  icon: '🌟',
                  title: t('features.items.accurate.title'),
                  description: t('features.items.accurate.description')
                },
                {
                  icon: '🎯',
                  title: t('features.items.personalized.title'),
                  description: t('features.items.personalized.description')
                },
                {
                  icon: '🛡️',
                  title: t('features.items.privacy.title'),
                  description: t('features.items.privacy.description')
                },
                {
                  icon: '📱',
                  title: t('features.items.crossPlatform.title'),
                  description: t('features.items.crossPlatform.description')
                }
              ].map((feature, index) => (
                <div
                  key={index}
                  className="feature-card group relative overflow-hidden rounded-2xl border border-white/10 p-8 backdrop-blur-sm"
                  style={{
                    background: 'linear-gradient(145deg, rgba(255,255,255,0.05), rgba(255,255,255,0.02))'
                  }}
                >
                  <div className="mb-4 text-5xl">{feature.icon}</div>
                  <h3 className="mb-4 text-xl font-bold text-white transition-colors duration-300 group-hover:text-purple-300">
                    {feature.title}
                  </h3>
                  <p className="leading-relaxed text-gray-400 transition-colors duration-300 group-hover:text-gray-300">
                    {feature.description}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </section>
        {/* 占卜牌阵 */}
        <section className="relative py-20">
          <div className="container mx-auto px-4">
            <div className="mb-16 text-center">
              <h2 className="mb-4 bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-4xl font-bold text-transparent text-white">
                {t('spreads.title')}
              </h2>
              <p className="mx-auto max-w-2xl text-xl text-gray-300">{t('spreads.description')}</p>
            </div>
            <SpreadList />
          </div>
        </section>
        {/* CTA区域 */}
        <section className="relative overflow-hidden py-20">
          <div className="absolute inset-0"></div>
          <div className="relative z-10 container mx-auto px-4 text-center">
            <h2 className="mb-6 text-4xl font-bold text-white">{t('cta.title')}</h2>
            <p className="mx-auto mb-8 max-w-2xl text-xl text-gray-300">{t('cta.description')}</p>
            <div className="flex flex-col items-center justify-center gap-4 sm:flex-row">
              <Link
                href="/question"
                className="group relative inline-flex items-center justify-center overflow-hidden rounded-full bg-gradient-to-r from-purple-600 to-pink-600 px-8 py-4 text-lg font-medium text-white transition-all duration-300 hover:scale-105 hover:shadow-2xl hover:shadow-purple-500/25"
              >
                <span className="relative z-10">{t('cta.startButton')}</span>
                <div className="absolute inset-0 bg-gradient-to-r from-pink-600 to-purple-600 opacity-0 transition-opacity duration-300 group-hover:opacity-100"></div>
              </Link>
              <Link
                href="/records"
                className="inline-flex items-center justify-center rounded-full border-2 border-purple-400 px-8 py-4 text-lg font-medium text-purple-300 transition-all duration-300 hover:scale-105 hover:bg-purple-400 hover:text-white"
              >
                {t('cta.historyButton')}
              </Link>
            </div>
          </div>
        </section>
      </div>
    </div>
  )
}
