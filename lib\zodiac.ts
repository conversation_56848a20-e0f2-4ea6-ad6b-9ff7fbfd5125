// 星座计算工具函数

export interface ZodiacSign {
  name: string
  nameEn: string
  symbol: string
  element: string
  dateRange: string
}

// 十二星座数据
export const zodiacSigns: ZodiacSign[] = [
  {
    name: '白羊座',
    nameEn: 'Aries',
    symbol: '♈',
    element: '火',
    dateRange: '3月21日 - 4月19日'
  },
  {
    name: '金牛座',
    nameEn: 'Taurus',
    symbol: '♉',
    element: '土',
    dateRange: '4月20日 - 5月20日'
  },
  {
    name: '双子座',
    nameEn: 'Gemini',
    symbol: '♊',
    element: '风',
    dateRange: '5月21日 - 6月20日'
  },
  {
    name: '巨蟹座',
    nameEn: 'Cancer',
    symbol: '♋',
    element: '水',
    dateRange: '6月21日 - 7月22日'
  },
  {
    name: '狮子座',
    nameEn: 'Leo',
    symbol: '♌',
    element: '火',
    dateRange: '7月23日 - 8月22日'
  },
  {
    name: '处女座',
    nameEn: 'Virgo',
    symbol: '♍',
    element: '土',
    dateRange: '8月23日 - 9月22日'
  },
  {
    name: '天秤座',
    nameEn: 'Libra',
    symbol: '♎',
    element: '风',
    dateRange: '9月23日 - 10月22日'
  },
  {
    name: '天蝎座',
    nameEn: 'Scorpio',
    symbol: '♏',
    element: '水',
    dateRange: '10月23日 - 11月21日'
  },
  {
    name: '射手座',
    nameEn: 'Sagittarius',
    symbol: '♐',
    element: '火',
    dateRange: '11月22日 - 12月21日'
  },
  {
    name: '摩羯座',
    nameEn: 'Capricorn',
    symbol: '♑',
    element: '土',
    dateRange: '12月22日 - 1月19日'
  },
  {
    name: '水瓶座',
    nameEn: 'Aquarius',
    symbol: '♒',
    element: '风',
    dateRange: '1月20日 - 2月18日'
  },
  {
    name: '双鱼座',
    nameEn: 'Pisces',
    symbol: '♓',
    element: '水',
    dateRange: '2月19日 - 3月20日'
  }
]

/**
 * 根据生日计算星座
 * @param birthday 生日字符串，格式：YYYY-MM-DD
 * @returns 星座信息，如果生日格式错误则返回 null
 */
export function getZodiacSign(birthday: string): ZodiacSign | null {
  if (!birthday || !/^\d{4}-\d{2}-\d{2}$/.test(birthday)) {
    return null
  }

  const [year, month, day] = birthday.split('-').map(Number)
  
  // 验证日期有效性
  const date = new Date(year, month - 1, day)
  if (date.getFullYear() !== year || date.getMonth() !== month - 1 || date.getDate() !== day) {
    return null
  }

  // 星座判断逻辑
  if ((month === 3 && day >= 21) || (month === 4 && day <= 19)) {
    return zodiacSigns[0] // 白羊座
  } else if ((month === 4 && day >= 20) || (month === 5 && day <= 20)) {
    return zodiacSigns[1] // 金牛座
  } else if ((month === 5 && day >= 21) || (month === 6 && day <= 20)) {
    return zodiacSigns[2] // 双子座
  } else if ((month === 6 && day >= 21) || (month === 7 && day <= 22)) {
    return zodiacSigns[3] // 巨蟹座
  } else if ((month === 7 && day >= 23) || (month === 8 && day <= 22)) {
    return zodiacSigns[4] // 狮子座
  } else if ((month === 8 && day >= 23) || (month === 9 && day <= 22)) {
    return zodiacSigns[5] // 处女座
  } else if ((month === 9 && day >= 23) || (month === 10 && day <= 22)) {
    return zodiacSigns[6] // 天秤座
  } else if ((month === 10 && day >= 23) || (month === 11 && day <= 21)) {
    return zodiacSigns[7] // 天蝎座
  } else if ((month === 11 && day >= 22) || (month === 12 && day <= 21)) {
    return zodiacSigns[8] // 射手座
  } else if ((month === 12 && day >= 22) || (month === 1 && day <= 19)) {
    return zodiacSigns[9] // 摩羯座
  } else if ((month === 1 && day >= 20) || (month === 2 && day <= 18)) {
    return zodiacSigns[10] // 水瓶座
  } else if ((month === 2 && day >= 19) || (month === 3 && day <= 20)) {
    return zodiacSigns[11] // 双鱼座
  }

  return null
}

/**
 * 格式化星座显示文本
 * @param zodiacSign 星座信息
 * @returns 格式化的星座文本
 */
export function formatZodiacText(zodiacSign: ZodiacSign): string {
  return `${zodiacSign.symbol} ${zodiacSign.name}`
}
