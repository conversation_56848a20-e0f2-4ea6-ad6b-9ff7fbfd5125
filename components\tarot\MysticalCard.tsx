'use client'

import { useEffect, useRef } from 'react'

export default function MysticalCard({
  width,
  height,
  card,
  onClick
}: {
  width: string
  height: string
  card?: CardType | null
  onClick?: () => void
}) {
  const containerRef = useRef<HTMLDivElement>(null)
  const cardRef = useRef<HTMLDivElement>(null)
  const imgRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const container = containerRef.current
    const card = cardRef.current
    const img = imgRef.current
    if (!container || !card || !img) return

    const multiple = 15

    const transformElement = (x: number, y: number) => {
      const box = card.getBoundingClientRect()
      const calcX = -(y - box.y - box.height / 2) / multiple
      const calcY = (x - box.x - box.width / 2) / multiple

      card.style.transform = `rotateX(${calcX}deg) rotateY(${calcY}deg)`
    }

    const resetTransform = () => {
      window.requestAnimationFrame(() => {
        card.style.transform = 'rotateX(0) rotateY(0)'
      })
    }

    // 鼠标事件处理
    const handleMouseMove = (e: MouseEvent) => {
      window.requestAnimationFrame(() => {
        transformElement(e.clientX, e.clientY)
      })
    }

    const handleMouseLeave = () => {
      resetTransform()
    }

    // 陀螺仪事件处理
    const handleDeviceOrientation = (event: DeviceOrientationEvent) => {
      if (event.beta === null || event.gamma === null) return

      // 将陀螺仪数据转换为屏幕坐标
      const centerX = window.innerWidth / 2
      const centerY = window.innerHeight / 2

      // beta: -180 到 180 (前后倾斜)
      // gamma: -90 到 90 (左右倾斜)
      const sensitivity = 2
      const x = centerX + event.gamma * sensitivity
      const y = centerY + event.beta * sensitivity

      window.requestAnimationFrame(() => {
        transformElement(x, y)
      })
    }

    // 检测是否为移动设备
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
    const isTouchDevice = 'ontouchstart' in window

    if (isMobile || isTouchDevice) {
      // 移动端：使用陀螺仪
      if (window.DeviceOrientationEvent) {
        // 请求陀螺仪权限（iOS 13+ 需要）
        if (typeof (DeviceOrientationEvent as any).requestPermission === 'function') {
          // 显示权限请求提示
          const requestPermission = async () => {
            try {
              const permission = await (DeviceOrientationEvent as any).requestPermission()
              if (permission === 'granted') {
                window.addEventListener('deviceorientation', handleDeviceOrientation)
              }
            } catch (error) {
              // 陀螺仪权限被拒绝，静默处理
            }
          }

          // 添加点击事件来触发权限请求
          const handleTouchStart = () => {
            requestPermission()
            container.removeEventListener('touchstart', handleTouchStart)
          }
          container.addEventListener('touchstart', handleTouchStart)
        } else {
          // 直接添加陀螺仪监听
          window.addEventListener('deviceorientation', handleDeviceOrientation)
        }
      }
    } else {
      // 桌面端：使用鼠标事件
      container.addEventListener('mousemove', handleMouseMove)
      container.addEventListener('mouseleave', handleMouseLeave)
    }

    return () => {
      container.removeEventListener('mousemove', handleMouseMove)
      container.removeEventListener('mouseleave', handleMouseLeave)
      window.removeEventListener('deviceorientation', handleDeviceOrientation)
    }
  }, [])

  return (
    <div
      ref={containerRef}
      className="flex h-[100%] w-[100%] cursor-pointer items-center justify-center transform-3d"
      style={{
        perspective: '1000px'
      }}
    >
      <div
        ref={cardRef}
        className="card-container mx-auto transform-gpu rounded-lg bg-gradient-to-br from-black via-pink-600 to-purple-600 transition-all duration-100 transform-3d"
        style={{
          width: width,
          height: height
        }}
        onClick={onClick}
      >
        {card ? (
          <div
            ref={imgRef}
            className="g-img group relative h-full w-full rounded-lg bg-cover bg-center brightness-125"
            style={{
              backgroundImage: `url(${card.link})`
            }}
          >
            {/* <img src={card.link} alt="" srcset="" /> */}
          </div>
        ) : (
          <div
            ref={imgRef}
            className="g-img group relative h-full w-full rounded-lg bg-cover bg-center brightness-125"
            style={{
              backgroundImage: "url('https://static.destinyai.tools/tarot/card-backs/home_cardbg.png')"
            }}
          >
            <div
              className="absolute inset-0 rounded-lg opacity-100 mix-blend-color-dodge transition-opacity duration-300"
              style={{
                backgroundImage: "url('https://s3-us-west-2.amazonaws.com/s.cdpn.io/13471/sparkles.gif')"
              }}
            ></div>
            <div className="absolute inset-4 rounded-xl border-2 border-purple-400/30"></div>
          </div>
        )}
      </div>
    </div>
  )
}
