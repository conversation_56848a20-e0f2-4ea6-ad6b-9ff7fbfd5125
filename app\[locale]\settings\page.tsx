'use client'

import { Lock } from 'lucide-react'
import { useSession } from 'next-auth/react'
import { useState, useEffect } from 'react'

import { updateUserCardBack } from '@/actions/profile'
import {
  AlertDialog,
  AlertDialogTrigger,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogFooter
} from '@/components/ui/alert-dialog'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { TooltipProvider } from '@/components/ui/tooltip'

const FREE_CARD_BACKS = ['https://static.destinyai.tools/tarot/card-backs/card_bgm.png']

const VIP_CARD_BACKS = [
  '/cardsbg/cardbg1.png',
  '/cardsbg/cardbg2.png',
  '/cardsbg/cardbg3.png',
  '/cardsbg/cardbg4.png',
  '/cardsbg/cardbg5.png',
  '/cardsbg/cardbg6.png',
  '/cardsbg/cardbg7.png',
  '/cardsbg/cardbg8.png'
]

export default function SettingsPage() {
  const { data: session, update } = useSession()
  const userCardBack = (session && 'user' in session && (session.user as any)?.cardBackUrl) || FREE_CARD_BACKS[0]
  const [selected, setSelected] = useState(userCardBack)
  const [showDialog, setShowDialog] = useState(false)

  // 假设isVip为false，实际应从用户信息获取
  const isVip = false
  const goVIP = () => {
    window.location.href = '/membership'
  }

  useEffect(() => {
    if (selected && selected !== userCardBack) {
      updateUserCardBack(selected).then(() => {
        update() // 刷新session
      })
    }
  }, [selected])

  return (
    <TooltipProvider>
      <div className="container mx-auto max-w-3xl px-2 py-8">
        <div className="mb-8 text-center">
          <h1 className="text-3xl font-bold text-white">牌背设置</h1>
          <p className="mt-2 text-gray-300">选择你喜欢的牌背样式，用于抽牌环节</p>
        </div>

        {/* 免费牌背 */}
        <div className="mb-8">
          <div className="mb-4 flex items-center gap-2">
            <Badge variant="success" className="px-3 py-1 text-lg font-bold">
              🎁 免费牌背
            </Badge>
          </div>
          <RadioGroup
            className="mb-8 grid w-full grid-cols-3 gap-x-2 gap-y-6 md:gap-x-4 md:gap-y-12"
            value={selected}
            onValueChange={setSelected}
          >
            {FREE_CARD_BACKS.map((url) => (
              <div key={url} className="flex max-w-[140px] cursor-pointer flex-col items-center gap-2">
                <label htmlFor={url} className="">
                  <img
                    src={url}
                    alt={url}
                    className="aspect-[140/240] w-full cursor-pointer rounded-lg border-2 object-cover shadow-lg"
                  />
                  <div className="mt-2 text-center text-base font-bold text-white">免费牌背</div>
                </label>
                <RadioGroupItem
                  value={url}
                  className="data-[state=checked]:border-primary data-[state=checked]:bg-primary/10 mt-4 h-5 w-5 cursor-pointer border-2 data-[state=checked]:shadow-lg"
                  id={url}
                >
                  <span className="bg-primary m-auto block h-4 w-4 rounded-full transition-transform data-[state=checked]:scale-110" />
                </RadioGroupItem>
              </div>
            ))}
          </RadioGroup>
        </div>

        {/* 会员专属牌背 */}
        <div>
          <div className="mb-4 flex items-center gap-2">
            <Badge variant="secondary" className="px-3 py-1 text-lg font-bold">
              ✨ 会员专属牌背
            </Badge>
            {!isVip && (
              <AlertDialog open={showDialog} onOpenChange={setShowDialog}>
                <AlertDialogTrigger asChild>
                  <Button size="sm" className="ml-2" onClick={() => setShowDialog(true)}>
                    解锁 <Lock className="ml-1 h-4 w-4" />
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>会员专属功能</AlertDialogTitle>
                    <AlertDialogDescription>该牌背仅限会员解锁，开通会员后可自由选择专属牌背。</AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>取消</AlertDialogCancel>
                    <AlertDialogAction onClick={goVIP}>去开通会员</AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            )}
          </div>
          <div className="mb-8 grid w-full grid-cols-3 gap-x-2 gap-y-6 md:gap-x-4 md:gap-y-12">
            {VIP_CARD_BACKS.map((src, idx) => (
              <div
                key={src}
                className="flex max-w-[140px] cursor-pointer flex-col items-center gap-2"
                onClick={() => {
                  if (isVip) setSelected(src)
                  else setShowDialog(true)
                }}
              >
                <img
                  src={src}
                  alt={`会员牌背${idx + 1}`}
                  className="aspect-[140/240] w-full cursor-pointer rounded-lg border-2 object-cover shadow-lg"
                />
                <div className="mt-2 text-center text-base font-bold text-white">会员牌背{idx + 1}</div>
                {!isVip && (
                  <div className="mt-4">
                    <Lock className="h-4 w-4 text-white" />
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>
    </TooltipProvider>
  )
}
