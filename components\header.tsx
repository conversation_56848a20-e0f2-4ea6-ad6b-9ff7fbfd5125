import { Menu, User, History, BarChart3, Calendar, Crown, Settings, BookImage } from 'lucide-react'
import { getTranslations } from 'next-intl/server'

import { LocaleSwitcher } from '@/components/locale-switcher'
import LoginModal from '@/components/login/login-modal'
import Logo from '@/components/logo'
import { MainNav } from '@/components/main-nav'
import { Sheet, SheetContent, SheetDescription, SheetHeader, SheetTitle, SheetTrigger } from '@/components/ui/sheet'
import { checkUserIsAdmin } from '@/hooks/use-is-admin'
import { Link } from '@/i18n/navigation'
import { auth } from '@/lib/auth'
import { cn } from '@/lib/utils'

// 图标映射
const iconMap = {
  User: User,
  History: History,
  BarChart3: BarChart3,
  Calendar: Calendar,
  Crown: Crown,
  Settings: Settings,
  BookImage: BookImage
}

export default async function Header({ className }: { className?: string }) {
  const t = await getTranslations('headers')
  const session = await auth()
  const isUserAdmin = await checkUserIsAdmin(session?.user?.id)

  const mobileLinkStyle = 'flex items-center rounded-md px-3 py-3 text-lg font-medium transition-colors'

  const navLinks = [
    { href: '/question', label: '塔罗占卜', icon: 'User' },
    { href: '/daily', label: '每日运势', icon: 'Calendar' },
    { href: '/membership', label: '会员订阅', icon: 'Crown' }
  ]
  if (session?.user) {
    navLinks.push({ href: '/records', label: '占卜记录', icon: 'History' })

    navLinks.push({ href: '/profile', label: '个人中心', icon: 'User' })
    navLinks.push({ href: '/settings', label: '卡背设置', icon: 'BookImage' })
  }

  if (isUserAdmin) {
    navLinks.push({ href: '/admin/analytics', label: '数据统计', icon: 'BarChart3' })
  }

  return (
    <header
      className={cn(
        'bg-background/95 supports-[backdrop-filter]:bg-background/60 fixed top-0 z-50 flex h-16 w-full items-center justify-between border-b px-4 backdrop-blur sm:px-6 lg:px-18',
        className
      )}
    >
      <nav className="hidden w-full items-center justify-between md:flex">
        <div className="flex items-center">
          <Logo />
        </div>

        <MainNav className="ml-4" items={navLinks} />
        <div className="flex w-[150px] items-center gap-6">
          <LocaleSwitcher />
          <LoginModal />
        </div>
      </nav>

      <div className="flex w-full items-center justify-between gap-4 md:hidden">
        <Logo />
        <div className="flex items-center">
          <div className="px-2">
            <LocaleSwitcher />
          </div>
          <div className="px-2">
            <LoginModal />
          </div>
          <div className="pl-2">
            <Sheet>
              <SheetTrigger asChild>
                <Menu className="size-6" />
              </SheetTrigger>
              <SheetContent side="right" className="flex w-[280px] flex-col sm:w-[320px]">
                <SheetHeader>
                  <SheetTitle>{t('navigation')}</SheetTitle>
                  <SheetDescription>{t('navigationDescription')}</SheetDescription>
                </SheetHeader>

                <div className="flex-1 overflow-y-auto">
                  <nav className="space-y-1">
                    {navLinks.map((link) => {
                      const IconComponent = link.icon ? iconMap[link.icon as keyof typeof iconMap] : null
                      return (
                        <Link key={link.href} href={link.href} className={mobileLinkStyle}>
                          {IconComponent && <IconComponent className="mr-3 h-5 w-5" />}
                          {link.label}
                        </Link>
                      )
                    })}
                  </nav>
                </div>

                {/* <div className="border-t pt-4">
                <LoginModal />
              </div> */}
              </SheetContent>
            </Sheet>
          </div>
        </div>
      </div>
    </header>
  )
}
