'use client'

import { Sparkles } from 'lucide-react'
import { useTranslations } from 'next-intl'

import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'

import InterpretationContent from './InterpretationContent'

const InterpretationDialog = ({
  isOpen,
  onClose,
  text,
  isLoading,
  cards = []
}: {
  isOpen: boolean
  onClose: () => void
  text: string
  isLoading: boolean
  cards?: CurrentCardType[]
}) => {
  const tInterpretation = useTranslations('interpretation')

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-h-[90vh] max-w-5xl overflow-hidden border-purple-400/30 bg-gradient-to-br from-slate-900 via-purple-900/20 to-slate-900 text-white backdrop-blur-xl">
        {/* 顶部标题栏 */}
        <DialogHeader className="relative border-b border-purple-400/20 pb-6">
          <DialogTitle className="flex items-center gap-3 text-3xl font-bold">
            <div className="rounded-full bg-gradient-to-r from-purple-500 to-pink-500 p-2">
              <Sparkles className="h-6 w-6 text-white" />
            </div>
            <span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
              {tInterpretation('title')}
            </span>
          </DialogTitle>
        </DialogHeader>

        <div className="dialog-scroll max-h-[70vh] overflow-y-auto pr-2">
          <InterpretationContent text={text} isLoading={isLoading} cards={cards} />
        </div>
      </DialogContent>
    </Dialog>
  )
}

export default InterpretationDialog
