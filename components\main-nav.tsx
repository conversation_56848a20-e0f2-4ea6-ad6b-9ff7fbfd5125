'use client'

import { User, History, BarChart3, Calendar, Crown, Settings, BookImage } from 'lucide-react'

import { Button } from '@/components/ui/button'
import { Link, usePathname } from '@/i18n/navigation'
import { cn } from '@/lib/utils'

// 图标映射
const iconMap = {
  User: User,
  History: History,
  BarChart3: BarChart3,
  Calendar: Calendar,
  Crown: Crown,
  Settings: Settings,
  BookImage: BookImage
}

export function MainNav({
  items,
  className,
  ...props
}: React.ComponentProps<'nav'> & {
  items: { href: string; label: string; icon?: string }[]
}) {
  const pathname = usePathname()

  return (
    <nav className={cn('items-center gap-0.5', className)} {...props}>
      {items.map((item) => {
        const IconComponent = item.icon ? iconMap[item.icon as keyof typeof iconMap] : null
        return (
          <Button key={item.href} variant="ghost" asChild size="default">
            <Link href={item.href} className={cn('flex items-center gap-2', pathname === item.href && 'text-primary')}>
              {IconComponent && <IconComponent className="h-4 w-4" />}
              {item.label}
            </Link>
          </Button>
        )
      })}
    </nav>
  )
}
