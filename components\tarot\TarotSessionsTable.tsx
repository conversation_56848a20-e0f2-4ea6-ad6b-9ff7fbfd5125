'use client'

import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import { useRouter } from '@/i18n/navigation'
import { formatDate } from '@/lib/utils'

interface TarotSession {
  id: string
  question: string
  spreadName: string
  spreadCategory: string
  spreadDesc: string | null
  reason: string | null
  cardCount: number
  spreadLink: string | null
  cards: string | null
  aiInterpretation: string | null
  status: string
  isDeleted?: number
  createdAt: number | Date
  completedAt: number | Date | null
  userName?: string | null
}

interface TarotSessionsTableProps {
  sessions: TarotSession[]
  readOnly?: boolean
}

export function TarotSessionsTable({ sessions, readOnly = false }: TarotSessionsTableProps) {
  const router = useRouter()

  const getStatusText = (status: string) => {
    switch (status) {
      case 'created':
        return '已创建'
      case 'drawing':
        return '抽牌中'
      case 'completed':
        return '已完成'
      default:
        return status
    }
  }

  const getStatusVariant = (status: string): 'default' | 'success' | 'secondary' | 'destructive' | 'outline' => {
    switch (status) {
      case 'created':
        return 'secondary'
      case 'drawing':
        return 'default'
      case 'completed':
        return 'success'
      default:
        return 'outline'
    }
  }

  return (
    <TooltipProvider>
      <div className="overflow-hidden rounded-lg border shadow">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>占卜问题</TableHead>
              <TableHead>用户名</TableHead>
              <TableHead>牌阵名称</TableHead>
              <TableHead>牌阵类别</TableHead>
              <TableHead>卡牌数量</TableHead>
              <TableHead>状态</TableHead>
              <TableHead>删除状态</TableHead>
              <TableHead>创建时间</TableHead>
              <TableHead>完成时间</TableHead>
              <TableHead className="w-[180px]">操作</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {sessions.length === 0 ? (
              <TableRow>
                <TableCell colSpan={10} className="text-muted-foreground text-center">
                  暂无占卜记录
                </TableCell>
              </TableRow>
            ) : (
              sessions.map((session) => (
                <TableRow key={session.id}>
                  <TableCell className="max-w-[200px]">
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <div className="hover:text-primary cursor-help truncate transition-colors">
                          {session.question}
                        </div>
                      </TooltipTrigger>
                      <TooltipContent side="top" className="max-w-sm">
                        <p className="break-words whitespace-normal">{session.question}</p>
                      </TooltipContent>
                    </Tooltip>
                  </TableCell>
                  <TableCell>{session.userName || '未知用户'}</TableCell>
                  <TableCell>{session.spreadName}</TableCell>
                  <TableCell>
                    <Badge variant="outline">{session.spreadCategory}</Badge>
                  </TableCell>
                  <TableCell>{session.cardCount}</TableCell>
                  <TableCell>
                    <Badge variant={getStatusVariant(session.status)}>{getStatusText(session.status)}</Badge>
                  </TableCell>
                  <TableCell>
                    <Badge variant={session.isDeleted === 1 ? 'destructive' : 'success'}>
                      {session.isDeleted === 1 ? '已删除' : '正常'}
                    </Badge>
                  </TableCell>
                  <TableCell>{formatDate(session.createdAt, 'yyyy-MM-dd HH:mm')}</TableCell>
                  <TableCell>
                    {session.completedAt ? formatDate(session.completedAt, 'yyyy-MM-dd HH:mm') : '-'}
                  </TableCell>
                  <TableCell>
                    <div className="flex space-x-2">
                      {!readOnly && session.status !== 'completed' && session.spreadLink && (
                        <Button
                          variant="default"
                          size="sm"
                          onClick={() => router.push(`/draw/${session.spreadLink}?sessionId=${session.id}`)}
                        >
                          继续占卜
                        </Button>
                      )}
                      <Button variant="outline" size="sm" onClick={() => router.push(`/records/${session.id}`)}>
                        详情
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>
    </TooltipProvider>
  )
}
