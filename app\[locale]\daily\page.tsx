'use client'

import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from 'lucide-react'
import { useSession } from 'next-auth/react'
import { useLocale } from 'next-intl'
import { useEffect, useState, useRef } from 'react'
import { toast } from 'sonner'

import { dailyInterpretation } from '@/actions/tarot'
import DailyCard from '@/components/tarot/DailyCard'
import { BirthdayPicker } from '@/components/ui/birthday-picker'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { useCards } from '@/lib/load-cards'
import { getZodiacSign, formatZodiacText } from '@/lib/zodiac'

interface UserInfo {
  birthday?: string
  gender?: 'male' | 'female' | 'other'
}

export default function DailyPage() {
  const { data: session } = useSession()
  const [dailyFortune, setDailyFortune] = useState<{
    card: CardType
    interpretation: string
    timestamp: number
  } | null>(null)
  const [countdown, setCountdown] = useState('00:00:00')
  const [isInterpreting, setIsInterpreting] = useState(false)
  const [userInfo, setUserInfo] = useState<UserInfo | null>(null)
  const [isUpdatingBirthday, setIsUpdatingBirthday] = useState(false)
  const locale = useLocale()
  const { cards } = useCards(locale)
  const [curCard, setCurCard] = useState<CardType | null>(null)
  const interpretationRef = useRef<string>('')

  // 获取用户信息
  useEffect(() => {
    async function fetchUserInfo() {
      if (!session?.user?.id) return
      try {
        const res = await fetch('/api/profile')
        if (res.ok) {
          const data = (await res.json()) as { personalInfo?: UserInfo }
          setUserInfo(data.personalInfo || {})
        }
      } catch (error) {
        console.error('Failed to fetch user info:', error)
      }
    }
    fetchUserInfo()
  }, [session?.user?.id])

  // 拉取今日运势
  useEffect(() => {
    async function fetchDaily() {
      if (!session?.user?.id) return
      const res = await fetch('/api/daily-fortune')
      const data: any = await res.json()
      if (data && data.card && data.interpretation && data.timestamp) {
        setDailyFortune({
          card: JSON.parse(data.card),
          interpretation: data.interpretation,
          timestamp: data.timestamp
        })
        setCurCard(JSON.parse(data.card))
      } else {
        setDailyFortune(null)
      }
    }
    fetchDaily()
    // 倒计时
    const calculateCountdown = () => {
      const now = new Date()
      const tomorrow = new Date(now)
      tomorrow.setDate(tomorrow.getDate() + 1)
      tomorrow.setHours(0, 0, 0, 0)
      const diff = tomorrow.getTime() - now.getTime()
      const hours = Math.floor(diff / (1000 * 60 * 60))
      const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))
      const seconds = Math.floor((diff % (1000 * 60)) / 1000)
      setCountdown(
        `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
      )
    }
    calculateCountdown()
    const interval = setInterval(calculateCountdown, 1000)
    return () => clearInterval(interval)
  }, [session?.user?.id])

  // 更新生日
  const handleBirthdayUpdate = async (birthday: string) => {
    if (!session?.user?.id) return
    setIsUpdatingBirthday(true)
    try {
      const res = await fetch('/api/profile', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ birthday })
      })
      if (res.ok) {
        const data = (await res.json()) as { personalInfo?: UserInfo }
        setUserInfo(data.personalInfo || {})
        toast.success('生日信息已保存！')
      } else {
        toast.error('保存失败，请稍后重试')
      }
    } catch (error) {
      console.error('Failed to update birthday:', error)
      toast.error('保存失败，请稍后重试')
    } finally {
      setIsUpdatingBirthday(false)
    }
  }

  // 抽牌
  const selectCard = () => {
    if (curCard || dailyFortune) return
    if (!cards.length) return
    const randomNumber = Math.floor(Math.random() * cards.length)
    setCurCard(cards[randomNumber])
  }

  // 解答
  const handleViewFortune = async () => {
    if (!curCard) {
      toast.error('请先翻牌')
      return
    }
    if (dailyFortune) return
    setIsInterpreting(true)
    try {
      // 调用Gemini解读API
      const interpretation = await dailyInterpretation(
        {
          question: '今日运势',
          spreadName: '单牌',
          spreads: [{ ...curCard, direction: 'upright' }],
          card: curCard
        },
        locale
      )
      interpretationRef.current = interpretation || ''
      setDailyFortune({ card: curCard, interpretation: interpretation || '', timestamp: Date.now() })
      // 存入后端
      await fetch('/api/daily-fortune', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ card: curCard, interpretation, timestamp: Date.now() })
      })
      toast.success('今日运势已解答！')
    } catch (error) {
      console.error('Failed to interpret:', error)
      toast.error('解答失败，请稍后重试')
    } finally {
      setIsInterpreting(false)
    }
  }

  return (
    <div className="container mx-auto min-h-screen px-4 py-8">
      <div className="mx-auto max-w-md space-y-6">
        {/* 标题区域 */}
        <div className="space-y-2 text-center">
          <h1 className="text-3xl font-bold text-white">今日运势</h1>
          <p className="text-lg text-purple-200">AI星座塔罗占卜</p>
          {/* 星座信息或生日设置提示 */}
          {userInfo?.birthday ? (
            (() => {
              const zodiacSign = getZodiacSign(userInfo.birthday)
              return zodiacSign ? (
                <div className="inline-flex items-center gap-2 rounded-full bg-purple-500/20 px-4 py-2 text-sm text-purple-200">
                  <Sparkles className="h-4 w-4" />
                  <span>您的星座：{formatZodiacText(zodiacSign)}</span>
                </div>
              ) : (
                <div className="inline-flex items-center gap-2 rounded-full bg-purple-500/20 px-4 py-2 text-sm text-purple-200">
                  <Sparkles className="h-4 w-4" />
                  <span>生日信息已设置</span>
                </div>
              )
            })()
          ) : (
            <BirthdayPicker
              value={userInfo?.birthday}
              onValueChange={handleBirthdayUpdate}
              disabled={isUpdatingBirthday}
              trigger={
                <div className="inline-flex cursor-pointer items-center gap-2 rounded-full bg-purple-500/20 px-4 py-2 text-sm text-purple-200 transition-colors hover:bg-purple-500/30">
                  <Sparkles className="h-4 w-4" />
                  <span>添加生日信息解读更准确喔!</span>
                </div>
              }
            />
          )}
        </div>
        {/* 状态和倒计时 */}
        {dailyFortune && (
          <Card className="border-purple-400/20 bg-purple-900/30 text-white">
            <CardContent className="space-y-2 p-4 text-center">
              <p className="text-sm">您今天已经解答过每日运势</p>
              <div className="flex items-center justify-center gap-2 text-purple-200">
                <Clock className="h-4 w-4" />
                <span className="text-sm">下次预测倒计时: {countdown}</span>
              </div>
            </CardContent>
          </Card>
        )}
        {/* 塔罗牌展示 */}
        <div className="space-y-4">
          <DailyCard
            onClick={selectCard}
            card={curCard}
            interpretation={dailyFortune?.interpretation || interpretationRef.current}
          />
          {/* 查看运势按钮，仅未解答且已翻牌时显示 */}
          {!dailyFortune && curCard && (
            <Button
              onClick={handleViewFortune}
              disabled={isInterpreting}
              className="w-full bg-gradient-to-r from-purple-600 to-purple-700 py-3 text-lg font-medium text-white hover:from-purple-700 hover:to-purple-800"
            >
              {isInterpreting ? (
                '解答中...'
              ) : (
                <>
                  <Eye className="mr-2 h-5 w-5" />
                  查看今日运势
                </>
              )}
            </Button>
          )}
        </div>
        {/* 抽取按钮，仅未解答且未翻牌时显示 */}
        {!dailyFortune && !curCard && (
          <Button
            onClick={selectCard}
            className="w-full bg-gradient-to-r from-purple-600 to-purple-700 py-3 text-lg font-medium text-white hover:from-purple-700 hover:to-purple-800"
          >
            <Sparkles className="mr-2 h-5 w-5" />
            抽取今日运势
          </Button>
        )}
      </div>
    </div>
  )
}
