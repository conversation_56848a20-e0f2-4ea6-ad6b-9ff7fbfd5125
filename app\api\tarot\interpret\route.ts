import { GoogleGenAI } from '@google/genai'
import { NextRequest, NextResponse } from 'next/server'

import { hasEnoughTokens, updateUserTokenUsage } from '@/actions/token-management'
import { auth } from '@/lib/auth'
import { getLanguageNameFromLocale } from '@/lib/utils'

interface InterpretRequest {
  question: string
  spreadName: string
  spreads: any[]
  spreadDesc?: string
  locale?: string
}

export async function POST(request: NextRequest) {
  const session = await auth()
  if (!session?.user?.id) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
  }

  const hasTokens = await hasEnoughTokens(session.user.id, 0)

  if (!hasTokens) {
    return NextResponse.json({ error: 'Not enough tokens' }, { status: 402 })
  }
  try {
    const body = (await request.json()) as InterpretRequest
    const { question, spreadName, spreads, spreadDesc, locale = 'en' } = body

    if (!question || !spreadName || !spreads || !Array.isArray(spreads)) {
      return NextResponse.json({ error: '缺少必要参数' }, { status: 400 })
    }

    const languageName = getLanguageNameFromLocale(locale)

    const ai = new GoogleGenAI({
      apiKey: process.env.GEMINI_API_KEY!
    })

    const systemPrompt = `You are an experienced, compassionate tarot reader and AI assistant.
Please provide a professional, insightful, and inspiring tarot reading based on the user's question, selected spread, and revealed cards.
The reading should include interpretation of each card's meaning in its specific position, as well as a comprehensive summary and specific actionable advice.
Present in clear, positive language, avoiding overly fatalistic statements, and emphasizing the user's agency and choice.

Please respond entirely in ${languageName} language (locale: ${locale}).

IMPORTANT: Start your response directly with the tarot reading content. Do not use conversational phrases like "好的，没问题", "Of course", "Sure", or similar acknowledgments. Begin immediately with the interpretation.

Reading Guidelines:
1. Analyze each card individually in its position
2. Provide an overall interpretation that connects all cards
3. Offer practical, actionable advice
4. Use encouraging and empowering language
5. Acknowledge the user's free will and ability to shape their destiny
6. Structure the reading with clear sections for easy reading

Format your response with clear paragraphs and sections. Start with individual card interpretations, then provide an overall reading and actionable guidance.`

    let userPrompt = `User's Question: "${question}"
Selected Spread: "${spreadName}"${spreadDesc ? `\nSpread Description: "${spreadDesc}"` : ''}

Revealed Cards:
`

    spreads.forEach((pos: any, index: number) => {
      const direction = pos.direction === 'reversed' ? 'Reversed' : 'Upright'
      userPrompt += `\nPosition ${index + 1}: ${pos.name} (${direction})`
    })

    userPrompt += `\n\nPlease provide a complete tarot reading in ${languageName}, analyzing each card's significance in its position and offering comprehensive guidance.`

    const model = 'gemini-2.0-flash-exp'

    const chat = ai.chats.create({
      model: model,
      config: {
        maxOutputTokens: 8192,
        temperature: 0.7,
        systemInstruction: {
          parts: [{ text: systemPrompt }]
        }
      }
    })

    // 用于统计token消耗
    let totalTokensUsed = 0

    // 创建流式响应
    const stream = new ReadableStream({
      async start(controller) {
        try {
          const result = await chat.sendMessageStream({
            message: [{ text: userPrompt }]
          })

          let isFirstChunk = true

          for await (const chunk of result) {
            const text = chunk.text
            if (text) {
              // 发送文本块
              controller.enqueue(new TextEncoder().encode(`data: ${JSON.stringify({ text })}\n\n`))
            }

            // 在第一个chunk中获取使用的token数量
            if (isFirstChunk && chunk.usageMetadata) {
              const inputTokens = chunk.usageMetadata.promptTokenCount || 0
              const outputTokens = chunk.usageMetadata.candidatesTokenCount || 0
              totalTokensUsed = inputTokens + outputTokens
              isFirstChunk = false
            }
          }

          // 更新用户token使用量
          if (totalTokensUsed > 0 && session.user?.id) {
            try {
              await updateUserTokenUsage(session.user.id, totalTokensUsed)
            } catch (error) {
              console.error('Failed to update token usage:', error)
            }
          }

          // 发送结束标记
          controller.enqueue(new TextEncoder().encode('data: [DONE]\n\n'))
          controller.close()
        } catch (error) {
          console.error('Stream error:', error)

          // 根据语言返回错误信息
          const errorMessage = locale === 'zh' ? '解读过程中发生错误' : 'Error occurred during interpretation'
          controller.enqueue(new TextEncoder().encode(`data: ${JSON.stringify({ error: errorMessage })}\n\n`))
          controller.close()
        }
      }
    })

    return new Response(stream, {
      headers: {
        'Content-Type': 'text/plain; charset=utf-8',
        'Cache-Control': 'no-cache',
        Connection: 'keep-alive'
      }
    })
  } catch (error) {
    console.error('API Error:', error)

    // 根据语言返回错误信息 - 使用默认错误信息，因为此时无法安全获取locale
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
