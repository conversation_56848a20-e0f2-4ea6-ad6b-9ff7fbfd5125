{"headers": {"blogs": "Blog", "home": "Homepage", "about": "About", "submitTools": "Submit Tool", "navigation": "Website Navigation", "navigationDescription": "Visit the main sections of our website"}, "siteInfo": {"brandName": "Destiny AI", "meta": {"title": "Destiny AI - AI Tarot Divination Tool", "description": "Discover the best AI-powered divination tools like Tarot cards, astrology, I Ching, etc. Your guide to understanding modern divination techniques."}}, "divinationCategories": {"tarot": {"name": "Tarot card reading", "description": "A method of divination that uses a deck of 78 cards with symbolic images to gain insights into the past, present, and future.", "origin": "Italy/France (15th century Europe)", "seoTitle": "AI Tarot Card Reading - Free Online Card Meanings & Predictions", "seoDescription": "Get accurate AI-powered Tarot card readings online. Explore love life, career paths and the future revealed by the cards with our advanced Tarot card prediction tool."}, "astrology": {"name": "Astrology", "description": "Study of how the positions and movements of celestial bodies influence human affairs and natural phenomena.", "origin": "Multiple origins (Ancient Babylon, Egypt, Greece)", "seoTitle": "AI Astrology Charts & Horoscopes - Personalized Zodiac Interpretations", "seoDescription": "Explore your cosmic destiny with AI-powered astrology tools. Get personalized birth charts, daily horoscopes, and compatibility readings based on your zodiac sign."}, "iChing": {"name": "<PERSON>", "description": "An ancient Chinese divination system that uses hexagrams to provide guidance and predict future events.", "origin": "China (c. 1000–750 BC)", "seoTitle": "AI I Ching Divination - I Ching Interpretations & Hexagrams", "seoDescription": "Consult the ancient I Ching with our AI I Ching Divination. Get personalized hexagram interpretations, explanations, and guidance for important life decisions."}, "numerology": {"name": "Numerology", "description": "Belief in a sacred or mystical connection between numbers and events, revealing information about personality and the future through calculations.", "origin": "Multiple origins (Ancient Greece, Egypt, Babylon)", "seoTitle": "AI Numerology Calculator - Life Path and Destiny Number Analysis", "seoDescription": "Use our AI numerology tool to calculate your life path, destiny, and expression numbers. Discover how numbers influence your personality, relationships, and future."}, "palmistry": {"name": "Palmistry", "description": "Identifying people and predicting the future by studying the lines, shapes, and textures of the palm.", "origin": "India (approximately 4000 years ago)", "seoTitle": "AI Palm Reading - Numerology and Hand Analysis", "seoDescription": "Upload a photo to perform an AI palm reading analysis. Discover how your hand lines reveal your love life, career success, health, and future with our digital palm reading tools."}, "dreamInterpretation": {"name": "Dream Interpretation", "description": "Analyze dreams to reveal hidden meanings, information, and predictions for the future.", "origin": "Ancient Egypt, Greece, and Mesopotamia", "seoTitle": "AI Dream Analysis - The Meaning of Dreams and Symbols", "seoDescription": "Understand your dreams through AI dream analysis. Get personalized interpretations of dream symbols, recurring themes, and subconscious information in sleep."}, "vedic": {"name": "Vedic Astrology", "description": "An ancient Indian astrological system based on stellar positions rather than regression positions, offering a detailed analysis of destiny, personality, and life events.", "origin": "India (c. 1500 BCE)", "seoTitle": "AI Vedic Astrology - J<PERSON><PERSON>h Birth Chart & Predictions", "seoDescription": "Discover your true cosmic blueprint through AI Vedic Astrology. Get accurate Jyotish interpretations, a birth chart, and predictions for career, relationships, and life path."}, "categoryNotFound": "Category not found", "categoryNotFoundDescription": "The divination category you are looking for was not found. Please check our homepage for available categories.", "other": {"name": "Other", "description": "Various alternative divination techniques and mystical practices not covered by traditional categories.", "origin": "Various cultures around the world", "seoTitle": "Alternative AI divination tools - Unique methods of fortune-telling", "seoDescription": "Explore unconventional AI divination methods and unique fortune-telling techniques. Discover rare mystical practices and alternative pathways to understanding destiny."}, "comprehensive": {"name": "Comprehensive", "description": "A platform offering multiple divination methods and integrated fortune-telling approaches in one place.", "origin": "Modern integration of traditional practices", "seoTitle": "Integrated AI divination platform - Multiple methods of fortune-telling", "seoDescription": "Access multiple AI divination methods in one place. The comprehensive platform offers tarot, astrology, numerology, and more, providing comprehensive spiritual guidance and fortune-telling services."}}, "footer": {"description": "Destiny AI - Your comprehensive artificial intelligence tarot card divination tool.", "contact": {"title": "Contact us", "intro": "If you have any questions, please contact:", "email": "<EMAIL>"}, "quickLinks": {"title": "Quick links", "home": "Homepage", "aboutUs": "About us", "refundPolicy": "Refund policy", "subscriptionTerms": "Subscription terms"}, "categories": "Category", "copyright": "© 2025 Destiny AI Tools All rights reserved."}, "login": {"signOut": "Log out", "login": "Log in", "modal": {"description": "Log in to continue your journey", "triggerText": "Log in"}, "form": {"continueWithGoogle": "Continue with Google", "continueWithGithub": "Continue with GitHub", "orContinueWith": "Or continue", "emailPlaceholder": "Enter your email", "signInWithEmail": "Sign in with email"}}, "common": {"loading": "Loading...", "cancel": "Cancel", "close": "Close", "previous": "Previous page", "next": "Next page", "save": "Save", "edit": "Edit", "delete": "Delete", "submit": "Submit", "morePages": "View more pages", "language": "Language", "selectLanguage": "Select language", "loadingSpread": "Loading card data..."}, "tarot": {"title": "AI Tarot Interpretation", "description": "Explore the unknown, gain guidance, and unlock inner wisdom.", "start": "Start a tarot card reading", "startAi": "Start an AI tarot card reading", "desc": "Introduction", "upright": "Upright", "reversed": "Reversed"}, "question": {"title": "Select your tarot card layout", "description": "Enter the question you want to predict, and the AI will recommend the most suitable layout for you.", "placeholder": "Please describe the problem you would like to have divined, for example: How can my career develop in the future to be successful...", "isLogin": "Start the divination journey after logging in", "isRecommending": "Analyzing the problem...", "start": "Enter a question to get an AI-recommended card layout", "dialogTitle": "AI Intelligent Recommendation", "recommendation": "Recommended Card Layout", "startDraw": "Start drawing cards", "reSelectSpread": "Select other card layouts yourself", "selectSpread": "Select this card layout", "login": "Log in", "loginDescription": "Please log in to continue using the divination function.", "spreadInfo": {"title": "Card layout interpretation", "positionMeaning": "Meaning of the positions", "meaning": "Meaning", "understood": "I understand"}}, "draw": {"dialogTitle": "Divination information", "title": "AI personalized interpretation", "viewResult": "View Interpretation Results", "loading": "Generating interpretation for you, please wait...", "error": "Sorry, an error occurred during interpretation, please try again later.", "turnAll": "Please flip all the cards", "selectCard": "You need to select {count} cards", "selected": "Selected: {current} / {total}", "leftRight": "View cards left and right", "complete": "Card selection completed! Entering the card flipping stage...", "shuffle": "Shuffle", "shuffleDesc": "Please long press and drag to shuffle"}, "interpretation": {"title": "AI Tarot Interpretation", "loadingMessage": "AI is deciphering the mysteries of the Tarot cards for you...", "loadingSubtitle": "Please wait, wisdom is gathering", "yourQuestion": "Your question:", "drawnCards": "Drawn cards:", "spreadReading": "Card layout interpretation", "aiDepthReading": "AI deep interpretation • Personal exclusive", "generatingContent": "Interpreting content is being generated...", "flipAllCards": "Please turn over all the cards and then click the interpretation button"}, "interpretationKeywordsLegend": {"legendTitle": "Keyword Legend", "cards": "Tarot Cards", "emotions": "Emotion", "career": "Career", "time": "Time", "states": "State", "advice": "Suggestion", "emphasis": "Emphasis"}, "profile": {"title": "Personal center", "loginRequired": "Please log in first to view the personal center", "loading": "Loading...", "description": "Explore your digital destiny, track your mysterious journey", "tokenUsage": {"title": "Token usage", "remaining": "Remaining tokens", "used": "Used Tokens", "total": "Total Tokens", "unlimited": "Unlimited"}, "personalInfo": {"title": "Personal Information", "birthday": "Birthday", "gender": "Gender", "male": "Male", "female": "Female", "other": "Other", "notSet": "Not set", "edit": "Edit", "save": "Save", "cancel": "Cancel", "birthdayPlaceholder": "Please select birthday", "genderPlaceholder": "Please select gender"}, "tarotHistory": {"title": "Divination record", "noRecords": "No divination records", "question": "Question", "spread": "Card spread", "status": "State", "date": "Time", "viewDetails": "View details", "statusCreated": "Created", "statusDrawing": "Drawing cards", "statusCompleted": "Completed", "loadMore": "Load more"}, "settings": {"title": "Account settings", "logout": "Log out"}}, "records": {"title": "Divination record", "description": "Track your mysterious journey and relive every guidance of fate.", "totalRecords": "Total of {count} records", "noRecords": "No divination records", "startJourney": "Start your first tarot reading journey", "table": {"question": "Divination question", "spread": "Card spread name", "category": "Card spread category", "cardCount": "Number of cards", "status": "State", "createdAt": "Creation time", "completedAt": "Completion time", "actions": "Operation", "viewDetails": "Details"}, "status": {"completed": "Completed", "drawing": "Drawing cards", "created": "Created", "unknown": "Unknown"}, "subtitle": "Track your mysterious journey and relive every guidance of fate.", "startFirstReading": "Start your first tarot reading journey", "startDivination": "Start divination", "loading": "Loading...", "viewDetails": "View details", "view": "View", "delete": "Delete", "deleteConfirm": "Are you sure you want to delete this record?", "deleteSuccess": "Successfully deleted", "cards": "Card", "showing": "Show", "recordsText": "Records", "detail": {"title": "占卜记录详情", "question": "占卜问题：", "spread": "牌阵：", "category": "类别：", "cardCount": "卡牌数量：", "status": "状态：", "createdAt": "创建时间：", "completedAt": "完成时间："}, "page": {"continueDivination": "Continue divination", "deleteError": "Deletion failed, please try again", "deleteConfirm": "Are you sure you want to delete this record?"}}, "home": {"hero": {"title": "AI Tarot", "subtitle": "Diviner", "description": "Fusing ancient wisdom with modern AI technology to reveal the mysteries of fate and guide the course of your life.", "features": {"aiReading": "🔮 AI Intelligent Interpretation", "instant": "⚡ Instant Divination", "accurate": "🌟 Accurate prediction"}, "startButton": "✨ Start divination", "stats": {"users": "User trust", "readings": "Number of divinations", "accuracy": "Accuracy rate"}}, "features": {"title": "Why choose us", "description": "We combine traditional Tarot wisdom with cutting-edge AI technology to provide you with the most accurate and considerate divination experience.", "items": {"aiReading": {"title": "AI intelligent interpretation", "description": "Combining machine learning algorithms, in-depth analysis of Tarot card meanings, providing accurate personalized interpretations."}, "instant": {"title": "Instant divination", "description": "No need to wait, get divination results instantly, and explore the answers of your heart anytime, anywhere."}, "accurate": {"title": "Accurate prediction", "description": "Based on the training of tens of millions of divination data, the accuracy is up to 99%, trustworthy."}, "personalized": {"title": "Personalized analysis", "description": "For your problems and background, we provide customized divination advice and life guidance."}, "privacy": {"title": "Privacy protection", "description": "We strictly protect user privacy, and all divination records are only visible to you, safe and reliable."}, "crossPlatform": {"title": "Cross-platform support", "description": "Supports synchronization between mobile phones, tablets, and computers, so you can practice divination anytime, anywhere."}}}, "spreads": {"title": "Divination card spreads", "description": "Choose the divination method that suits you and explore the mysteries of fate."}, "cta": {"title": "Are you ready to start your divination journey?", "description": "Let AI Tarot cards reveal the answers of your heart and guide you in the right direction.", "startButton": "Start divination now", "historyButton": "View History"}}, "components": {"ui": {"reset": "Reset"}, "tarot": {"cardInfo": {"cardBack": "Card Back"}, "drawCard": {"loadingCards": "Loading card data...", "continueDivination": "Continue divination", "networkError": "Network request failed", "streamError": "Unable to read response stream", "updateError": "Failed to update AI interpretation results", "interpretationError": "Interpretation failed"}, "questionSpread": {"title": "Divination information", "spreadName": "Spread Name:", "yourQuestion": "Your question:", "spreadDescription": "Spread Description:", "recommendationReason": "Recommended Reason:", "noInfo": "No divination information available"}, "sessionsTable": {"statusCreated": "Created", "statusDrawing": "Drawing cards", "statusCompleted": "Completed", "headers": {"question": "Divination question", "userName": "Username", "spreadName": "Card spread name", "spreadCategory": "Card spread category", "cardCount": "Number of cards", "status": "State", "deletedStatus": "Deletion Status", "createdAt": "Creation time", "completedAt": "Completion time", "actions": "Operation"}, "noRecords": "No divination records", "unknownUser": "Unknown User", "deleted": "Deleted", "normal": "Normal", "continueDivination": "Continue divination", "details": "Details"}, "spreadPreview": {"cardCount": "Card"}}}}