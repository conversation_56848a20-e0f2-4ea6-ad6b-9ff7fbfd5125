import { useState, useEffect } from 'react'

/**
 * 动态加载对应语言的塔罗牌阵数据
 * @param locale 语言代码
 * @returns Promise<SpreadClass[]> 牌阵数据数组
 */
export async function loadSpreads(locale: string = 'en'): Promise<SpreadClass[]> {
  try {
    const response = await fetch(`/spreads/${locale}.json`)
    if (response.ok) {
      return await response.json()
    } else {
      // 如果当前语言的文件不存在，回退到英语
      console.warn(`Spreads file for locale ${locale} not found, falling back to English`)
      const fallbackResponse = await fetch('/spreads/en.json')
      return await fallbackResponse.json()
    }
  } catch (error) {
    console.error('Error loading spreads:', error)
    // 加载失败时回退到英语
    try {
      const fallbackResponse = await fetch('/spreads/en.json')
      return await fallbackResponse.json()
    } catch (fallbackError) {
      console.error('Error loading fallback spreads:', fallbackError)
      return []
    }
  }
}

/**
 * React Hook: 动态加载对应语言的塔罗牌阵数据
 * @param locale 语言代码
 * @returns {spreads, loading} 牌阵数据和加载状态
 */
export function useSpreads(locale: string = 'en') {
  const [spreads, setSpreads] = useState<SpreadClass[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const load = async () => {
      setLoading(true)
      try {
        const data = await loadSpreads(locale)
        setSpreads(data)
      } finally {
        setLoading(false)
      }
    }

    load()
  }, [locale])

  return { spreads, loading }
}
