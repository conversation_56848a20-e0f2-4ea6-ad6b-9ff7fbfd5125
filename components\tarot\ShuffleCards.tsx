'use client'

import { useSession } from 'next-auth/react'
import { useTranslations } from 'next-intl'
import { useState, useEffect, useRef, useCallback, useMemo, memo } from 'react'

interface ShuffleCardsProps {
  onShuffleComplete: () => void
}

interface CardTransform {
  x: number
  y: number
  rotation: number
}

// 单独的卡牌组件，使用memo优化
const Card = memo(
  ({
    transform,
    index,
    isDragging,
    userCardBackUrl
  }: {
    transform: CardTransform
    index: number
    isDragging: boolean
    userCardBackUrl: string
  }) => {
    const style = useMemo(
      () => ({
        transform: `translate(${transform.x}px, ${transform.y}px) rotate(${transform.rotation}deg)`,
        zIndex: 10 - index,
        transition: isDragging ? 'transform 1s ease-out' : 'transform 1s cubic-bezier(0.4, 0, 0.2, 1)',
        backgroundImage: `url(${userCardBackUrl})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center'
      }),
      [transform.x, transform.y, transform.rotation, index, isDragging]
    )

    return (
      <div
        className="pointer-events-none absolute top-0 left-0 h-48 w-30 rounded-lg border-2 border-white/20 bg-gradient-to-br from-blue-600 to-purple-600"
        style={style}
      />
    )
  }
)

Card.displayName = 'Card'

export default function ShuffleCards({ onShuffleComplete }: ShuffleCardsProps) {
  const t = useTranslations('draw')
  const session = useSession()

  const [isDragging, setIsDragging] = useState(false)
  const [lastPosition, setLastPosition] = useState({ x: 0, y: 0 })
  const [totalDistance, setTotalDistance] = useState(0)
  const [progress, setProgress] = useState(0)

  // 修复数组长度不一致问题，统一使用20个卡牌
  const CARD_COUNT = 40
  const [cardTransforms, setCardTransforms] = useState<CardTransform[]>(
    Array(CARD_COUNT)
      .fill(null)
      .map(() => ({ x: 0, y: 0, rotation: 0 }))
  )

  const containerRef = useRef<HTMLDivElement>(null)
  const lastUpdateTime = useRef<number>(0)
  const animationFrameRef = useRef<number | undefined>(undefined)

  const REQUIRED_DISTANCE = 1000 // 需要移动1000px的距离
  const PROGRESS_THRESHOLD = 100 // 进度达到100%时完成洗牌
  const UPDATE_INTERVAL = 100 // 每100ms更新一次卡牌位置，避免抖动

  // 使用useMemo缓存常量计算
  const resetTransforms = useMemo(
    () =>
      Array(CARD_COUNT)
        .fill(null)
        .map(() => ({ x: 0, y: 0, rotation: 0 })),
    [CARD_COUNT]
  )

  // 计算两点之间的距离
  const calculateDistance = useCallback((point1: { x: number; y: number }, point2: { x: number; y: number }) => {
    const dx = point2.x - point1.x
    const dy = point2.y - point1.y
    return Math.sqrt(dx * dx + dy * dy)
  }, [])

  // 获取位置信息的通用函数
  const getPosition = useCallback((e: MouseEvent | TouchEvent) => {
    if ('touches' in e) {
      return { x: e.touches[0].clientX, y: e.touches[0].clientY }
    }
    return { x: e.clientX, y: e.clientY }
  }, [])

  // 生成新的卡牌变换
  const generateNewTransforms = useCallback(() => {
    return Array(CARD_COUNT)
      .fill(null)
      .map(() => ({
        x: (Math.random() - 0.5) * 300, // 增大位移范围：-150px到150px
        y: (Math.random() - 0.5) * 300, // 增大位移范围：-150px到150px
        rotation: (Math.random() - 0.5) * 240 // 增大旋转范围：-120到120度
      }))
  }, [CARD_COUNT])

  // 开始拖拽
  const startDrag = useCallback((x: number, y: number) => {
    if (containerRef.current) {
      setIsDragging(true)
      setLastPosition({ x, y })
      setTotalDistance(0)
      setProgress(0)
      lastUpdateTime.current = Date.now()
    }
  }, [])

  // 处理鼠标按下
  const handleMouseDown = useCallback(
    (e: React.MouseEvent) => {
      startDrag(e.clientX, e.clientY)
    },
    [startDrag]
  )

  // 处理触摸开始
  const handleTouchStart = useCallback(
    (e: React.TouchEvent) => {
      e.preventDefault() // 阻止默认行为，避免滚动
      const touch = e.touches[0]
      startDrag(touch.clientX, touch.clientY)
    },
    [startDrag]
  )

  // 处理移动 - 优化状态更新频率
  const handleMove = useCallback(
    (e: MouseEvent | TouchEvent) => {
      if (!isDragging || !containerRef.current) return

      const currentPos = getPosition(e)
      const currentTime = Date.now()

      // 计算移动距离
      const distance = calculateDistance(lastPosition, currentPos)

      // 只有移动距离足够大时才更新
      if (distance > 2) {
        setTotalDistance((prev) => {
          const newDistance = prev + distance
          const newProgress = Math.min((newDistance / REQUIRED_DISTANCE) * 100, 100)
          setProgress(newProgress)
          return newDistance
        })

        // 节流更新卡牌位置，避免抖动 - 使用requestAnimationFrame优化
        if (currentTime - lastUpdateTime.current > UPDATE_INTERVAL) {
          if (animationFrameRef.current) {
            cancelAnimationFrame(animationFrameRef.current)
          }

          animationFrameRef.current = requestAnimationFrame(() => {
            setCardTransforms(generateNewTransforms())
          })

          lastUpdateTime.current = currentTime
        }

        setLastPosition(currentPos)
      }
    },
    [isDragging, lastPosition, calculateDistance, getPosition, generateNewTransforms]
  )

  // 处理鼠标移动
  const handleMouseMove = useCallback(
    (e: MouseEvent) => {
      handleMove(e)
    },
    [handleMove]
  )

  // 处理触摸移动
  const handleTouchMove = useCallback(
    (e: TouchEvent) => {
      e.preventDefault() // 阻止默认行为，避免滚动
      handleMove(e)
    },
    [handleMove]
  )

  // 结束拖拽
  const endDrag = useCallback(() => {
    setIsDragging(false)

    // 清理动画帧
    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current)
    }

    // 重置所有卡牌的位置和旋转
    setCardTransforms(resetTransforms)

    if (progress >= PROGRESS_THRESHOLD) {
      setTimeout(() => {
        onShuffleComplete()
      }, 1000)
    }
  }, [progress, onShuffleComplete, resetTransforms])

  // 处理鼠标释放
  const handleMouseUp = useCallback(() => {
    endDrag()
  }, [endDrag])

  // 处理触摸结束
  const handleTouchEnd = useCallback(
    (e: TouchEvent) => {
      e.preventDefault()
      endDrag()
    },
    [endDrag]
  )

  // 添加全局事件监听
  useEffect(() => {
    if (isDragging) {
      // 鼠标事件
      document.addEventListener('mousemove', handleMouseMove)
      document.addEventListener('mouseup', handleMouseUp)

      // 触摸事件
      document.addEventListener('touchmove', handleTouchMove, { passive: false })
      document.addEventListener('touchend', handleTouchEnd, { passive: false })

      return () => {
        document.removeEventListener('mousemove', handleMouseMove)
        document.removeEventListener('mouseup', handleMouseUp)
        document.removeEventListener('touchmove', handleTouchMove)
        document.removeEventListener('touchend', handleTouchEnd)
      }
    }
  }, [isDragging, handleMouseMove, handleMouseUp, handleTouchMove, handleTouchEnd])

  // 清理动画帧
  useEffect(() => {
    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current)
      }
    }
  }, [])

  const safeCardBackUrl =
    (session.data?.user as any)?.userCardBackUrl || 'https://static.destinyai.tools/tarot/card-bgm.png'

  return (
    <div
      ref={containerRef}
      className="relative flex cursor-grab touch-none flex-col items-center justify-center overflow-hidden select-none md:min-h-[80vh]"
      style={{ minHeight: 'calc(100vh - 56px - env(safe-area-inset-top))' }}
      onMouseDown={handleMouseDown}
      onTouchStart={handleTouchStart}
    >
      {/* 洗牌区域 */}
      <div className="relative h-48 w-30 shadow-lg">
        {cardTransforms.map((transform, i) => (
          <Card key={i} transform={transform} index={i} isDragging={isDragging} userCardBackUrl={safeCardBackUrl} />
        ))}
      </div>

      {/* 说明文字 */}
      <div className="absolute bottom-8 left-1/2 -translate-x-1/2 transform text-center text-white">
        <h2 className="mb-4 bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-3xl font-bold text-transparent select-none">
          {t('shuffle')}
        </h2>
        <p className="mb-4 text-xl select-none">{t('shuffleDesc')}</p>
      </div>
    </div>
  )
}
