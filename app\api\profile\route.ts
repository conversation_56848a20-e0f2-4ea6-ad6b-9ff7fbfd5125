import { eq } from 'drizzle-orm'
import { NextRequest, NextResponse } from 'next/server'

import { auth } from '@/lib/auth'
import { createDb } from '@/lib/db'
import { userUsage, users } from '@/lib/db/schema'

export async function GET() {
  try {
    const session = await auth()

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const userId = session.user.id
    const db = createDb()

    // 获取用户的token使用情况和个人信息
    const [userInfo] = await db.select().from(userUsage).where(eq(userUsage.userId, userId)).limit(1)
    // 获取用户主表信息
    const [user] = await db.select().from(users).where(eq(users.id, userId)).limit(1)
    let cardBackUrl = user?.cardBackUrl
    // 兜底：如果cardBackUrl为空，自动补默认并写回数据库
    if (!cardBackUrl) {
      cardBackUrl = 'https://static.destinyai.tools/tarot/card-backs/card_bgm.png'
      await db.update(users).set({ cardBackUrl }).where(eq(users.id, userId))
    }

    // 如果用户没有userUsage记录，创建一个默认的
    if (!userInfo) {
      const [newUserInfo] = await db
        .insert(userUsage)
        .values({
          userId,
          usedTokens: 0,
          totalTokens: 0
        })
        .returning()

      return NextResponse.json({
        tokenUsage: {
          used: newUserInfo.usedTokens,
          total: newUserInfo.totalTokens,
          remaining: newUserInfo.totalTokens - newUserInfo.usedTokens
        },
        personalInfo: {
          birthday: newUserInfo.birthday,
          gender: newUserInfo.gender
        }
      })
    }

    return NextResponse.json({
      tokenUsage: {
        used: userInfo.usedTokens,
        total: userInfo.totalTokens,
        remaining: userInfo.totalTokens - userInfo.usedTokens
      },
      personalInfo: {
        birthday: userInfo.birthday,
        gender: userInfo.gender
      },
      cardBackUrl
    })
  } catch (error) {
    console.error('Error fetching profile data:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function PUT(request: NextRequest) {
  try {
    const session = await auth()

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const userId = session.user.id
    const db = createDb()
    const body = (await request.json()) as {
      birthday?: string
      gender?: 'male' | 'female' | 'other'
      cardBackUrl?: string
    }
    const { birthday, gender, cardBackUrl } = body

    // 验证输入数据
    if (birthday && !/^\d{4}-\d{2}-\d{2}$/.test(birthday)) {
      return NextResponse.json({ error: 'Invalid birthday format. Use YYYY-MM-DD' }, { status: 400 })
    }

    if (gender && !['male', 'female', 'other'].includes(gender)) {
      return NextResponse.json({ error: 'Invalid gender value' }, { status: 400 })
    }

    // 更新用户个人信息
    const updateData: any = {}
    if (birthday !== undefined) updateData.birthday = birthday
    if (gender !== undefined) updateData.gender = gender

    // 新增：更新用户主表的cardBackUrl
    if (cardBackUrl !== undefined) {
      await db.update(users).set({ cardBackUrl }).where(eq(users.id, userId))
    }

    const [updatedUser] = await db.update(userUsage).set(updateData).where(eq(userUsage.userId, userId)).returning()

    if (!updatedUser) {
      // 如果用户记录不存在，创建一个新的
      const [newUser] = await db
        .insert(userUsage)
        .values({
          userId,
          usedTokens: 0,
          totalTokens: 0,
          birthday,
          gender
        })
        .returning()

      return NextResponse.json({
        personalInfo: {
          birthday: newUser.birthday,
          gender: newUser.gender
        }
      })
    }

    return NextResponse.json({
      personalInfo: {
        birthday: updatedUser.birthday,
        gender: updatedUser.gender
      }
    })
  } catch (error) {
    console.error('Error updating profile:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
