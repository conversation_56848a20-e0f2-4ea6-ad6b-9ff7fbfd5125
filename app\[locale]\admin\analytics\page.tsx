import { getAllSessions } from '@/actions/questions'
import { TarotSessionsTable } from '@/components/tarot/TarotSessionsTable'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { SimplePagination } from '@/components/ui/pagination'
import { ResetButton } from '@/components/ui/reset-button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'

export default async function AnalyticsPage({
  searchParams
}: {
  searchParams: Promise<{
    page?: string
    startDate?: string
    endDate?: string
    username?: string
    isDeleted?: string
  }>
}) {
  const { page, startDate, endDate, username, isDeleted } = await searchParams
  const currentPage = page ? parseInt(page) : 1
  const pageSize = 20

  const { sessions, pagination } = await getAllSessions({
    page: currentPage,
    pageSize,
    startDate,
    endDate,
    username,
    isDeleted
  })

  return (
    <div className="min-h-screen">
      <div className="container mx-auto space-y-8 py-8">
        <div className="space-y-4 text-center">
          <h1 className="from-primary via-primary bg-gradient-to-r to-purple-400 bg-clip-text text-4xl font-bold text-transparent">
            全部用户占卜记录
          </h1>
        </div>

        {/* 筛选表单 */}
        <Card className="border-primary/20 border bg-gradient-to-br backdrop-blur-sm">
          <CardHeader>
            <CardTitle>筛选条件</CardTitle>
            <CardDescription>根据时间、用户名、删除状态等条件筛选占卜记录</CardDescription>
          </CardHeader>
          <CardContent>
            <form method="GET" className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-5">
              <div className="space-y-2">
                <Label htmlFor="startDate">开始时间</Label>
                <Input id="startDate" name="startDate" type="date" defaultValue={startDate} className="w-full" />
              </div>

              <div className="space-y-2">
                <Label htmlFor="endDate">结束时间</Label>
                <Input id="endDate" name="endDate" type="date" defaultValue={endDate} className="w-full" />
              </div>

              <div className="space-y-2">
                <Label htmlFor="username">用户名</Label>
                <Input
                  id="username"
                  name="username"
                  placeholder="搜索用户名"
                  defaultValue={username}
                  className="w-full"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="isDeleted">删除状态</Label>
                <Select name="isDeleted" defaultValue={isDeleted || 'all'}>
                  <SelectTrigger>
                    <SelectValue placeholder="选择状态" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部</SelectItem>
                    <SelectItem value="0">未删除</SelectItem>
                    <SelectItem value="1">已删除</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>&nbsp;</Label>
                <div className="flex gap-2">
                  <Button type="submit" className="flex-1">
                    筛选
                  </Button>
                  <ResetButton />
                </div>
              </div>
            </form>
          </CardContent>
        </Card>

        <div className="mb-6 flex items-center justify-between">
          <div className="text-muted-foreground text-sm">共 {pagination.totalItems} 条记录</div>
        </div>
        <div className="border-primary/20 from-primary/5 rounded-lg border bg-gradient-to-br to-transparent backdrop-blur-sm">
          <TarotSessionsTable sessions={sessions} readOnly />
        </div>
        {pagination.totalPages > 1 && (
          <div className="mt-6 flex justify-center">
            <SimplePagination
              currentPage={pagination.currentPage}
              totalPages={pagination.totalPages}
              basePath="/admin/analytics"
              searchParams={{ startDate, endDate, username, isDeleted }}
            />
          </div>
        )}
      </div>
    </div>
  )
}
