{"$schema": "node_modules/wrangler/config-schema.json", "name": "taotreading", "main": ".open-next/worker.js", "compatibility_date": "2025-04-22", "compatibility_flags": ["nodejs_compat", "global_fetch_strictly_public"], "observability": {"logs": {"enabled": true}}, "assets": {"directory": ".open-next/assets", "binding": "ASSETS"}, "d1_databases": [{"binding": "DB", "database_name": "taotreading", "database_id": "c3bdca80-c34b-4d3b-9c95-a00e54c8a92f", "migrations_dir": "migrations"}], "ai": {"binding": "AI"}, "r2_buckets": [{"bucket_name": "next-inc-cache", "binding": "NEXT_INC_CACHE_R2_BUCKET"}, {"bucket_name": "static", "binding": "static"}], "durable_objects": {"bindings": [{"name": "NEXT_CACHE_DO_QUEUE", "class_name": "DOQueueHandler"}, {"name": "NEXT_TAG_CACHE_DO_SHARDED", "class_name": "DOShardedTagCache"}]}, "migrations": [{"tag": "v1", "new_sqlite_classes": ["DOQueueHandler", "DOShardedTagCache"]}], "services": [{"binding": "WORKER_SELF_REFERENCE", "service": "taotreading"}]}