'use client'

import { <PERSON><PERSON><PERSON>, Clock, CreditCard, Eye, Trash2 } from 'lucide-react'
import Image from 'next/image'
import { useSession } from 'next-auth/react'
import { useLocale, useTranslations } from 'next-intl'
import { useState, useEffect } from 'react'

import { getQuestions, deleteTarotSession } from '@/actions/questions'
import { Button } from '@/components/ui/button'
import { useRouter } from '@/i18n/navigation'
import { useCards } from '@/lib/load-cards'
import { formatDate } from '@/lib/utils'

// 扩展 TarotSession 类型以包含解析后的卡牌
interface ExtendedTarotSession extends TarotSession {
  parsedCards: CurrentCardType[]
}

export default function RecordsPage() {
  const t = useTranslations('records')
  const locale = useLocale()
  const router = useRouter()
  const [list, setList] = useState<ExtendedTarotSession[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const { cards } = useCards(locale)
  const currentPage = 1
  const pageSize = 10
  const session = useSession()

  useEffect(() => {
    async function getList() {
      try {
        setIsLoading(true)
        const { sessions } = await getQuestions({
          page: currentPage,
          pageSize
        })

        setList(
          sessions.map((el) => {
            let parsedCards: CurrentCardType[] = []

            // 安全解析卡牌数据
            if (el.cards) {
              try {
                const elCards = JSON.parse(el.cards)
                parsedCards = elCards.map((item: any) => ({
                  ...cards[item.index],
                  position: item.position,
                  flipped: item.flipped,
                  direction: item.direction,
                  index: item.index
                }))
              } catch (error) {
                console.error('Failed to parse cards:', error)
                parsedCards = []
              }
            }

            return {
              ...el,
              // 转换 createdAt 为 number
              createdAt: el.createdAt instanceof Date ? el.createdAt.getTime() : el.createdAt,
              updatedAt: el.updatedAt instanceof Date ? el.updatedAt.getTime() : el.updatedAt,
              completedAt: el.completedAt instanceof Date ? el.completedAt.getTime() : el.completedAt,
              parsedCards
            }
          })
        )
      } catch (error) {
        console.error('Failed to fetch sessions:', error)
        setList([])
      } finally {
        setIsLoading(false)
      }
    }

    if (Object.keys(cards).length > 0) {
      getList()
    }
  }, [currentPage, pageSize, cards])

  const handleViewRecord = (sessionId: string) => {
    router.push(`/records/${sessionId}`)
  }

  const handleDelete = async (sessionId: string) => {
    try {
      await deleteTarotSession(sessionId)
      // 重新获取列表
      const { sessions } = await getQuestions({
        page: currentPage,
        pageSize
      })

      setList(
        sessions.map((el) => {
          let parsedCards: CurrentCardType[] = []

          // 安全解析卡牌数据
          if (el.cards) {
            try {
              const elCards = JSON.parse(el.cards)
              parsedCards = elCards.map((item: any) => ({
                ...cards[item.index],
                position: item.position,
                flipped: item.flipped,
                direction: item.direction,
                index: item.index
              }))
            } catch (error) {
              console.error('Failed to parse cards:', error)
              parsedCards = []
            }
          }

          return {
            ...el,
            // 转换 createdAt 为 number
            createdAt: el.createdAt instanceof Date ? el.createdAt.getTime() : el.createdAt,
            updatedAt: el.updatedAt instanceof Date ? el.updatedAt.getTime() : el.updatedAt,
            completedAt: el.completedAt instanceof Date ? el.completedAt.getTime() : el.completedAt,
            parsedCards
          }
        })
      )
    } catch (error) {
      console.error('删除失败:', error)
      alert('删除失败，请重试')
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed':
        return t('status.completed')
      case 'drawing':
        return t('status.drawing')
      case 'created':
        return t('status.created')
      default:
        return t('status.unknown')
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'text-green-400 bg-green-400/10 border-green-400/20'
      case 'drawing':
        return 'text-yellow-400 bg-yellow-400/10 border-yellow-400/20'
      case 'created':
        return 'text-blue-400 bg-blue-400/10 border-blue-400/20'
      default:
        return 'text-gray-400 bg-gray-400/10 border-gray-400/20'
    }
  }

  const safeCardBackUrl =
    (session.data?.user as any)?.userCardBackUrl || 'https://static.destinyai.tools/tarot/card-bgm.png'

  return (
    <div className="min-h-screen">
      <div className="container mx-auto max-w-4xl px-4 py-6 md:px-6 md:py-8">
        {/* 页面标题 */}
        <div className="mb-8 space-y-4 text-center">
          <div className="relative inline-flex items-center justify-center">
            <div className="absolute inset-0 rounded-full bg-gradient-to-r from-purple-500/20 to-pink-500/20 blur-xl"></div>
            <div className="relative rounded-full bg-gradient-to-r from-purple-500/10 to-pink-500/10 p-4">
              <BookOpen className="h-8 w-8 text-purple-400" />
            </div>
          </div>
          <h1 className="bg-gradient-to-r from-purple-400 via-pink-400 to-purple-300 bg-clip-text text-3xl font-bold text-transparent md:text-4xl">
            {t('title')}
          </h1>
          <p className="mx-auto max-w-md text-gray-400">{t('subtitle')}</p>
        </div>

        {isLoading ? (
          <div className="flex items-center justify-center py-12">
            <div className="relative">
              <div className="h-12 w-12 rounded-full border-4 border-purple-500/30"></div>
              <div className="absolute top-0 h-12 w-12 animate-spin rounded-full border-4 border-transparent border-t-purple-500"></div>
            </div>
            <p className="ml-4 text-gray-400">{t('loading')}</p>
          </div>
        ) : list.length === 0 ? (
          <div className="py-12 text-center">
            <div className="relative mb-6 inline-flex items-center justify-center">
              <div className="absolute inset-0 rounded-full bg-gradient-to-r from-purple-500/20 to-pink-500/20 blur-xl"></div>
              <div className="relative rounded-full bg-gradient-to-r from-purple-500/10 to-pink-500/10 p-8">
                <BookOpen className="h-12 w-12 text-purple-400" />
              </div>
            </div>
            <h3 className="mb-2 text-xl font-semibold text-gray-300">{t('noRecords')}</h3>
            <p className="mb-6 text-gray-500">{t('startFirstReading')}</p>
            <Button
              onClick={() => router.push('/question')}
              className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700"
            >
              {t('startDivination')}
            </Button>
          </div>
        ) : (
          <div className="space-y-4">
            {list.map((item) => (
              <div
                key={item.id}
                className="group rounded-xl border border-purple-400/20 bg-gradient-to-r from-black/40 to-purple-900/20 p-4 backdrop-blur-sm transition-all duration-300 hover:border-purple-400/40 hover:shadow-lg hover:shadow-purple-500/10 md:p-6"
              >
                {/* 问题标题 */}
                <div className="mb-4">
                  <h3 className="line-clamp-2 text-lg font-semibold text-purple-200 md:text-xl">{item.question}</h3>
                </div>

                {/* 基本信息 */}
                <div className="mb-4 flex flex-col gap-2 md:flex-row md:items-center md:justify-between">
                  <div className="flex flex-wrap items-center gap-2 text-sm text-gray-400">
                    <div className="flex items-center gap-1">
                      <CreditCard className="h-4 w-4" />
                      <span>{item.spreadName}</span>
                    </div>
                    <div className="hidden md:block">•</div>
                    <div className="flex items-center gap-1">
                      <span>
                        {item.cardCount} {t('cards')}
                      </span>
                    </div>
                  </div>

                  <div className="flex items-center gap-3">
                    <div className="flex items-center gap-1 text-sm text-gray-400">
                      <Clock className="h-4 w-4" />
                      <span>{formatDate(item.createdAt, 'MM-dd HH:mm')}</span>
                    </div>
                    <div
                      className={`rounded-full border px-2 py-1 text-xs font-medium ${getStatusColor(item.status || 'created')}`}
                    >
                      {getStatusText(item.status || 'created')}
                    </div>
                  </div>
                </div>

                {/* 卡牌预览 */}
                {item.parsedCards && item.parsedCards.length > 0 && (
                  <div className="mb-4">
                    <div className="flex flex-wrap gap-2">
                      {item.parsedCards.slice(0, 6).map((card, idx) => (
                        <div key={idx} className="relative">
                          <div className="relative h-12 w-8 overflow-hidden rounded shadow-sm md:h-16 md:w-10">
                            <Image
                              src={card.flipped ? card.link : safeCardBackUrl}
                              alt={card.flipped ? card.name : '卡背'}
                              fill
                              className={`object-cover transition-transform duration-300 group-hover:scale-105 ${
                                card.flipped && card.direction === 'reversed' ? 'rotate-180' : ''
                              }`}
                              sizes="40px"
                            />
                          </div>
                          {/* 位置标号 */}
                          {/* <div className="absolute -top-2 -left-2 flex h-6 w-6 items-center justify-center rounded-full bg-gradient-to-r from-purple-500 to-pink-500 text-xs font-bold text-white shadow-lg">
                            {card.position}
                          </div> */}
                          {/* 逆位标识 - 只在翻转且逆位时显示 */}
                          {card.flipped && card.direction === 'reversed' && (
                            <div className="absolute -top-2 -right-2 flex h-6 w-6 items-center justify-center rounded-full bg-red-500 text-xs font-bold text-white shadow-lg">
                              ↓
                            </div>
                          )}
                        </div>
                      ))}
                      {item.parsedCards.length > 6 && (
                        <div className="flex h-12 w-8 items-center justify-center rounded border border-purple-400/30 bg-purple-900/20 text-xs text-purple-300 md:h-16 md:w-10">
                          +{item.parsedCards.length - 6}
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {/* 操作按钮 */}
                <div className="flex items-center justify-between gap-2">
                  <div className="text-xs text-gray-500">
                    {item.spreadCategory && `${item.spreadCategory} • `}
                    ID: {item.id.slice(-8)}
                  </div>

                  <div className="flex items-center gap-2">
                    {item.status !== 'completed' && item.spreadLink && (
                      <Button
                        variant="default"
                        size="sm"
                        onClick={() => router.push(`/draw/${item.spreadLink}?sessionId=${item.id}`)}
                      >
                        {t('page.continueDivination')}
                      </Button>
                    )}

                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleViewRecord(item.id)}
                      className="border-purple-400/30 bg-purple-900/20 text-purple-300 hover:border-purple-400/50 hover:bg-purple-900/40 hover:text-purple-200"
                    >
                      <Eye className="mr-1 h-4 w-4" />
                      {/* <span className="hidden md:inline">{t('viewDetails')}</span>
                      <span className="md:hidden">{t('view')}</span> */}
                      <span>{t('view')}</span>
                    </Button>

                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-gray-400 hover:text-red-400"
                      onClick={() => {
                        if (window.confirm(t('deleteConfirm') || '确定要删除这条记录吗？')) {
                          handleDelete(item.id)
                        }
                      }}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>
            ))}

            {/* 分页控件 (如果需要) */}
            <div className="mt-8 flex justify-center">
              <div className="text-center text-sm text-gray-500">
                {t('totalRecords', { count: String(list.length) })}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
