'use client'

import { useSearchParams } from 'next/navigation'
import { use, useEffect, useState } from 'react'

import CardSelection from '@/components/tarot/CardSelection'
import DrawCard from '@/components/tarot/DrawCard'
import ShuffleCards from '@/components/tarot/ShuffleCards'
import StageTransition from '@/components/tarot/StageTransition'

// 扩展全局 CardType，添加占卜时需要的额外属性

export default function DrawPage({ params }: { params: Promise<{ slug: string[] }> }) {
  const { slug } = use(params)
  const searchParams = useSearchParams()

  const sessionId = searchParams.get('sessionId')

  const [tarotSession, setTarotSession] = useState<TarotSession | null>(null)

  useEffect(() => {
    async function getTarotSession() {
      if (!sessionId) return

      try {
        const response = await fetch(`/api/tarot/session?id=${sessionId}`)
        if (response.ok) {
          const data: TarotSession = await response.json()
          setTarotSession(data)
          if (data.cards && data.cards.length) {
            // 用户已翻牌
            setCurrentStage('draw')
          }
        } else {
          console.error('获取塔罗会话失败:', response.status)
        }
      } catch (error) {
        console.error('获取塔罗会话出错:', error)
      }
    }
    getTarotSession()
  }, [sessionId])

  const [currentStage, setCurrentStage] = useState('shuffle')

  // 处理会话数据更新
  const handleSessionUpdate = (updatedSession: TarotSession) => {
    setTarotSession(updatedSession)
  }

  // 根据当前阶段渲染不同的内容
  const renderContent = () => {
    if (!tarotSession) return null

    switch (currentStage) {
      case 'shuffle':
        return <ShuffleCards onShuffleComplete={() => setCurrentStage('select')} />

      case 'select':
        return (
          <CardSelection
            requiredCount={tarotSession?.cardCount || 1}
            onCompleteSelection={() => setCurrentStage('draw')}
          />
        )

      case 'draw':
        return <DrawCard slug={slug} sessionData={tarotSession} onSessionUpdate={handleSessionUpdate} />
      default:
        return null
    }
  }

  return (
    <>
      <StageTransition stage={currentStage}>{renderContent()}</StageTransition>
    </>
  )
}
