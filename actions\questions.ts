'use server'

import { count, desc, eq, and } from 'drizzle-orm'

import { auth } from '@/lib/auth'
import { createDb } from '@/lib/db'
import { tarotSessions, users } from '@/lib/db/schema'

export async function getQuestions({ page = 1, pageSize = 10 }: { page?: number; pageSize?: number }) {
  const session = await auth()

  if (!session?.user?.id) {
    return {
      sessions: [],
      pagination: {
        currentPage: 1,
        pageSize,
        totalItems: 0,
        totalPages: 0
      }
    }
  }

  const database = createDb()
  const userId = session.user.id

  const currentPage = Math.max(1, page)
  const itemsPerPage = Math.max(1, pageSize)
  const offset = (currentPage - 1) * itemsPerPage

  const baseQuery = database
    .select()
    .from(tarotSessions)
    .where(and(eq(tarotSessions.userId, userId), eq(tarotSessions.isDeleted, 0)))
    .orderBy(desc(tarotSessions.createdAt))

  const query = baseQuery

  const countQuery = database
    .select({ count: count() })
    .from(tarotSessions)
    .where(and(eq(tarotSessions.userId, userId), eq(tarotSessions.isDeleted, 0)))

  const [sessions, countResult] = await Promise.all([query.limit(itemsPerPage).offset(offset), countQuery])

  const totalItems = countResult[0]?.count || 0
  const totalPages = Math.ceil(totalItems / itemsPerPage)

  return {
    sessions,
    pagination: {
      currentPage,
      pageSize: itemsPerPage,
      totalItems,
      totalPages
    }
  }
}

export async function getAllSessions({
  page = 1,
  pageSize = 20,
  startDate,
  endDate,
  username,
  isDeleted
}: {
  page?: number
  pageSize?: number
  startDate?: string
  endDate?: string
  username?: string
  isDeleted?: string
}) {
  const session = await auth()
  if (!session?.user?.id) {
    return {
      sessions: [],
      pagination: {
        currentPage: 1,
        pageSize,
        totalItems: 0,
        totalPages: 0
      }
    }
  }

  const database = createDb()
  const currentPage = Math.max(1, page)
  const itemsPerPage = Math.max(1, pageSize)
  const offset = (currentPage - 1) * itemsPerPage

  // 构建基础查询
  const baseQuery = database
    .select({
      id: tarotSessions.id,
      userId: tarotSessions.userId,
      question: tarotSessions.question,
      spreadName: tarotSessions.spreadName,
      spreadCategory: tarotSessions.spreadCategory,
      spreadDesc: tarotSessions.spreadDesc,
      reason: tarotSessions.reason,
      cardCount: tarotSessions.cardCount,
      spreadLink: tarotSessions.spreadLink,
      cards: tarotSessions.cards,
      aiInterpretation: tarotSessions.aiInterpretation,
      status: tarotSessions.status,
      isDeleted: tarotSessions.isDeleted,
      createdAt: tarotSessions.createdAt,
      updatedAt: tarotSessions.updatedAt,
      completedAt: tarotSessions.completedAt,
      userName: users.name
    })
    .from(tarotSessions)
    .leftJoin(users, eq(tarotSessions.userId, users.id))
    .orderBy(desc(tarotSessions.createdAt))

  const countQuery = database.select({ count: count() }).from(tarotSessions)

  const [allSessions, countResult] = await Promise.all([baseQuery, countQuery])

  // 在内存中进行筛选
  let filteredSessions = allSessions

  // 时间筛选
  if (startDate || endDate) {
    filteredSessions = filteredSessions.filter((session) => {
      const sessionDate = new Date(session.createdAt)

      if (startDate && sessionDate < new Date(startDate)) {
        return false
      }

      if (endDate && sessionDate > new Date(endDate + ' 23:59:59')) {
        return false
      }

      return true
    })
  }

  // 用户名筛选
  if (username) {
    filteredSessions = filteredSessions.filter((session) => session.userName && session.userName.includes(username))
  }

  // 删除状态筛选
  if (isDeleted !== undefined && isDeleted !== '' && isDeleted !== 'all') {
    const deletedFilter = parseInt(isDeleted)
    filteredSessions = filteredSessions.filter((session) => session.isDeleted === deletedFilter)
  }

  // 分页
  const totalItems = filteredSessions.length
  const totalPages = Math.ceil(totalItems / itemsPerPage)
  const sessions = filteredSessions.slice(offset, offset + itemsPerPage)

  return {
    sessions,
    pagination: {
      currentPage,
      pageSize: itemsPerPage,
      totalItems,
      totalPages
    }
  }
}

export async function getSessionById(sessionId: string) {
  const session = await auth()
  if (!session?.user?.id) return null
  const database = createDb()
  const userId = session.user.id
  const result = await database
    .select()
    .from(tarotSessions)
    .where(and(eq(tarotSessions.id, sessionId), eq(tarotSessions.userId, userId), eq(tarotSessions.isDeleted, 0)))
    .limit(1)
  return result[0] || null
}

// 软删除占卜记录
export async function deleteTarotSession(sessionId: string) {
  const session = await auth()
  if (!session?.user?.id) {
    throw new Error('未授权')
  }

  const database = createDb()
  const userId = session.user.id

  try {
    const result = await database
      .update(tarotSessions)
      .set({
        isDeleted: 1,
        updatedAt: new Date()
      })
      .where(and(eq(tarotSessions.id, sessionId), eq(tarotSessions.userId, userId)))

    return { success: true }
  } catch (error) {
    console.error('删除占卜记录失败:', error)
    throw new Error('删除失败')
  }
}
