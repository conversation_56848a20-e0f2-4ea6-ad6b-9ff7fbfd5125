'use client'

import { useState } from 'react'
import { toast } from 'sonner'

import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'

type Mode = 'phone' | 'email'

export default function ChinaLogin({ onSuccess }: { onSuccess?: () => void }) {
  const [mode, setMode] = useState<Mode>('phone')
  const [phone, setPhone] = useState('')
  const [code, setCode] = useState('')
  const [email, setEmail] = useState('')
  const [isSending, setIsSending] = useState(false)
  const [isVerifying, setIsVerifying] = useState(false)
  const [sent, setSent] = useState(false)

  // 模拟发送验证码
  const sendCode = async () => {
    if (!/^1[3-9]\d{9}$/.test(phone)) {
      toast.error('请输入有效的中国大陆手机号')
      return
    }
    setIsSending(true)
    setTimeout(() => {
      setSent(true)
      setIsSending(false)
      toast.success('验证码已发送（模拟）')
    }, 1000)
  }

  // 模拟手机号登录
  const verifyCode = async () => {
    if (code.length !== 6) {
      toast.error('请输入6位验证码')
      return
    }
    setIsVerifying(true)
    setTimeout(() => {
      setIsVerifying(false)
      toast.success('登录成功（模拟）')
      onSuccess?.()
    }, 1000)
  }

  // 邮箱登录（可对接后端）
  const emailLogin = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!/^[\w.-]+@[\w.-]+\.\w+$/.test(email)) {
      toast.error('请输入有效邮箱')
      return
    }
    setIsVerifying(true)
    setTimeout(() => {
      setIsVerifying(false)
      toast.success('邮箱登录成功（模拟）')
      onSuccess?.()
    }, 1000)
  }

  return (
    <div className="space-y-6">
      <div className="mb-4 flex justify-center gap-4">
        <Button variant={mode === 'phone' ? 'default' : 'outline'} onClick={() => setMode('phone')}>
          手机号登录
        </Button>
        <Button variant={mode === 'email' ? 'default' : 'outline'} onClick={() => setMode('email')}>
          邮箱登录
        </Button>
      </div>

      {mode === 'phone' && (
        <div className="space-y-4">
          <Input
            type="tel"
            placeholder="请输入手机号"
            value={phone}
            onChange={(e) => setPhone(e.target.value)}
            maxLength={11}
          />
          <div className="flex gap-2">
            <Input
              type="text"
              placeholder="验证码"
              value={code}
              onChange={(e) => setCode(e.target.value)}
              maxLength={6}
            />
            <Button onClick={sendCode} disabled={isSending || sent}>
              {isSending ? '发送中...' : sent ? '已发送' : '获取验证码'}
            </Button>
          </div>
          <Button onClick={verifyCode} disabled={isVerifying}>
            {isVerifying ? '登录中...' : '登录'}
          </Button>
        </div>
      )}

      {mode === 'email' && (
        <form className="space-y-4" onSubmit={emailLogin}>
          <Input type="email" placeholder="请输入邮箱" value={email} onChange={(e) => setEmail(e.target.value)} />
          <Button type="submit" disabled={isVerifying}>
            {isVerifying ? '登录中...' : '邮箱登录'}
          </Button>
        </form>
      )}
    </div>
  )
}
