import { useState, useEffect } from 'react'

/**
 * 动态加载对应语言的塔罗牌数据
 * @param locale 语言代码
 * @returns Promise<CardType[]> 卡牌数据数组
 */
export async function loadCards(locale: string = 'en'): Promise<CardType[]> {
  try {
    const response = await fetch(`/cards/${locale}.json`)
    if (response.ok) {
      return await response.json()
    } else {
      // 如果当前语言的文件不存在，回退到英语
      console.warn(`Cards file for locale ${locale} not found, falling back to English`)
      const fallbackResponse = await fetch('/cards/en.json')
      return await fallbackResponse.json()
    }
  } catch (error) {
    console.error('Error loading cards:', error)
    // 加载失败时回退到英语
    try {
      const fallbackResponse = await fetch('/cards/en.json')
      return await fallbackResponse.json()
    } catch (fallbackError) {
      console.error('Error loading fallback cards:', fallbackError)
      return []
    }
  }
}

/**
 * React Hook: 动态加载对应语言的塔罗牌数据
 * @param locale 语言代码
 * @returns {cards, loading} 卡牌数据和加载状态
 */
export function useCards(locale: string = 'en') {
  const [cards, setCards] = useState<CardType[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const load = async () => {
      setLoading(true)
      try {
        const data = await loadCards(locale)
        setCards(data)
      } finally {
        setLoading(false)
      }
    }

    load()
  }, [locale])

  return { cards, loading }
}
