{
  "extends": ["next/core-web-vitals", "next/typescript", "prettier"],
  "plugins": ["import", "unused-imports"],
  "ignorePatterns": ["scripts/**/*"],
  "rules": {
    "react/no-unescaped-entities": "off",
    // "react/jsx-no-literals": "error",
    "no-console": ["error", { "allow": ["error", "warn"] }],
    "@typescript-eslint/no-unused-vars": "off",
    "@typescript-eslint/no-explicit-any": "off",
    "@typescript-eslint/no-non-null-asserted-optional-chain": "off",
    "@typescript-eslint/no-empty-object-type": "off",
    "@next/next/no-img-element": "off",
    "import/order": [
      "error",
      {
        "groups": ["builtin", "external", "internal", ["parent", "sibling"], "index", "object", "type"],
        "newlines-between": "always",
        "alphabetize": { "order": "asc", "caseInsensitive": true }
      }
    ],
    "import/first": "error",
    "import/newline-after-import": "error",
    "import/no-duplicates": "error",
    "unused-imports/no-unused-imports": "error",
    "unused-imports/no-unused-vars": [
      "warn",
      { "vars": "all", "varsIgnorePattern": "^_", "args": "after-used", "argsIgnorePattern": "^_" }
    ],
    "no-restricted-imports": [
      "error",
      {
          "name": "next/link",
          "message": "Please import from `@/i18n/navigation` instead."
      },
      {
          "name": "next/navigation",
          "importNames": [
              "redirect",
              "permanentRedirect",
              "useRouter",
              "usePathname"
          ],
          "message": "Please import from `@/i18n/navigation` instead."
      }
  ]
  }
}
