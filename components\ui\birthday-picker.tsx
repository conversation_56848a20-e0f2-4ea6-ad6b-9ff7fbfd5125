'use client'

import { useState } from 'react'
import { Calendar, Check } from 'lucide-react'
import { useTranslations } from 'next-intl'

import { Button } from '@/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from '@/components/ui/dialog'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'

interface BirthdayPickerProps {
  value?: string // YYYY-MM-DD 格式
  onValueChange: (value: string) => void
  trigger?: React.ReactNode
  disabled?: boolean
}

export function BirthdayPicker({ value, onValueChange, trigger, disabled }: BirthdayPickerProps) {
  const t = useTranslations('components.ui.birthdayPicker')
  const [open, setOpen] = useState(false)
  const [tempYear, setTempYear] = useState('')
  const [tempMonth, setTempMonth] = useState('')
  const [tempDay, setTempDay] = useState('')

  // 解析当前值
  const parseValue = (val?: string) => {
    if (!val || !/^\d{4}-\d{2}-\d{2}$/.test(val)) {
      return { year: '', month: '', day: '' }
    }
    const [year, month, day] = val.split('-')
    return { year, month, day }
  }

  // 当对话框打开时，初始化临时值
  const handleOpenChange = (isOpen: boolean) => {
    setOpen(isOpen)
    if (isOpen) {
      const { year, month, day } = parseValue(value)
      setTempYear(year)
      setTempMonth(month)
      setTempDay(day)
    }
  }

  // 生成年份选项（1950-当前年份）
  const currentYear = new Date().getFullYear()
  const years = Array.from({ length: currentYear - 1949 }, (_, i) => currentYear - i)

  // 生成月份选项
  const months = Array.from({ length: 12 }, (_, i) => {
    const month = i + 1
    return {
      value: month.toString().padStart(2, '0'),
      label: `${month}${t('monthSuffix')}`
    }
  })

  // 生成日期选项（根据年月动态计算）
  const getDaysInMonth = (year: string, month: string) => {
    if (!year || !month) return 31
    const daysInMonth = new Date(parseInt(year), parseInt(month), 0).getDate()
    return daysInMonth
  }

  const days = Array.from({ length: getDaysInMonth(tempYear, tempMonth) }, (_, i) => {
    const day = i + 1
    return {
      value: day.toString().padStart(2, '0'),
      label: `${day}${t('daySuffix')}`
    }
  })

  // 确认选择
  const handleConfirm = () => {
    if (tempYear && tempMonth && tempDay) {
      const birthday = `${tempYear}-${tempMonth}-${tempDay}`
      onValueChange(birthday)
      setOpen(false)
    }
  }

  // 清除选择
  const handleClear = () => {
    setTempYear('')
    setTempMonth('')
    setTempDay('')
  }

  const isValid = tempYear && tempMonth && tempDay

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogTrigger asChild disabled={disabled}>
        {trigger || (
          <Button variant="outline" className="w-full justify-start text-left font-normal">
            <Calendar className="mr-2 h-4 w-4" />
            {value
              ? parseValue(value).year +
                t('yearSuffix') +
                parseValue(value).month +
                t('monthSuffix') +
                parseValue(value).day +
                t('daySuffix')
              : t('selectBirthday')}
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>{t('title')}</DialogTitle>
          <DialogDescription>{t('description')}</DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-3 gap-4">
            {/* 年份选择 */}
            <div className="space-y-2">
              <label className="text-sm font-medium">{t('year')}</label>
              <Select value={tempYear} onValueChange={setTempYear}>
                <SelectTrigger>
                  <SelectValue placeholder={t('yearPlaceholder')} />
                </SelectTrigger>
                <SelectContent className="max-h-[200px]">
                  {years.map((year) => (
                    <SelectItem key={year} value={year.toString()}>
                      {year}
                      {t('yearSuffix')}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* 月份选择 */}
            <div className="space-y-2">
              <label className="text-sm font-medium">{t('month')}</label>
              <Select value={tempMonth} onValueChange={setTempMonth}>
                <SelectTrigger>
                  <SelectValue placeholder={t('monthPlaceholder')} />
                </SelectTrigger>
                <SelectContent>
                  {months.map((month) => (
                    <SelectItem key={month.value} value={month.value}>
                      {month.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* 日期选择 */}
            <div className="space-y-2">
              <label className="text-sm font-medium">日期</label>
              <Select value={tempDay} onValueChange={setTempDay}>
                <SelectTrigger>
                  <SelectValue placeholder="日" />
                </SelectTrigger>
                <SelectContent className="max-h-[200px]">
                  {days.map((day) => (
                    <SelectItem key={day.value} value={day.value}>
                      {day.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>
        <DialogFooter className="gap-2">
          <Button variant="outline" onClick={handleClear}>
            清除
          </Button>
          <Button onClick={handleConfirm} disabled={!isValid}>
            <Check className="mr-2 h-4 w-4" />
            确认
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
