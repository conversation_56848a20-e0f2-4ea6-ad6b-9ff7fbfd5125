{"version": "6", "dialect": "sqlite", "id": "656d4c9d-43d0-4c81-9422-dc4d973c77ef", "prevId": "7f73dafe-e9b2-4fc8-a079-13e2d0179b76", "tables": {"account": {"name": "account", "columns": {"userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "provider": {"name": "provider", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "providerAccountId": {"name": "providerAccountId", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "refresh_token": {"name": "refresh_token", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "access_token": {"name": "access_token", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "expires_at": {"name": "expires_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "token_type": {"name": "token_type", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "scope": {"name": "scope", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "id_token": {"name": "id_token", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "session_state": {"name": "session_state", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"account_userId_user_id_fk": {"name": "account_userId_user_id_fk", "tableFrom": "account", "tableTo": "user", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {"account_provider_providerAccountId_pk": {"columns": ["provider", "providerAccountId"], "name": "account_provider_providerAccountId_pk"}}, "uniqueConstraints": {}, "checkConstraints": {}}, "daily_fortune": {"name": "daily_fortune", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "card": {"name": "card", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "interpretation": {"name": "interpretation", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "timestamp": {"name": "timestamp", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"daily_fortune_user_id_user_id_fk": {"name": "daily_fortune_user_id_user_id_fk", "tableFrom": "daily_fortune", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "session": {"name": "session", "columns": {"sessionToken": {"name": "sessionToken", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "expires": {"name": "expires", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"session_userId_user_id_fk": {"name": "session_userId_user_id_fk", "tableFrom": "session", "tableTo": "user", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "tarot_sessions": {"name": "tarot_sessions", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "question": {"name": "question", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "spread_name": {"name": "spread_name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "spread_category": {"name": "spread_category", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "spread_desc": {"name": "spread_desc", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "reason": {"name": "reason", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "card_count": {"name": "card_count", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "spread_link": {"name": "spread_link", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "cards": {"name": "cards", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "ai_interpretation": {"name": "ai_interpretation", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'created'"}, "is_deleted": {"name": "is_deleted", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "completed_at": {"name": "completed_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"tarot_sessions_user_id_user_id_fk": {"name": "tarot_sessions_user_id_user_id_fk", "tableFrom": "tarot_sessions", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "userUsage": {"name": "userUsage", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "usedTokens": {"name": "usedTokens", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "totalTokens": {"name": "totalTokens", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "birthday": {"name": "birthday", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "gender": {"name": "gender", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"userUsage_userId_user_id_fk": {"name": "userUsage_userId_user_id_fk", "tableFrom": "userUsage", "tableTo": "user", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "user": {"name": "user", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "emailVerified": {"name": "emailVerified", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "image": {"name": "image", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {"user_email_unique": {"name": "user_email_unique", "columns": ["email"], "isUnique": true}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "verificationToken": {"name": "verificationToken", "columns": {"identifier": {"name": "identifier", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "token": {"name": "token", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "expires": {"name": "expires", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"verificationToken_identifier_token_pk": {"columns": ["identifier", "token"], "name": "verificationToken_identifier_token_pk"}}, "uniqueConstraints": {}, "checkConstraints": {}}}, "views": {}, "enums": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"indexes": {}}}