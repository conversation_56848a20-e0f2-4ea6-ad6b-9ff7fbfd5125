import { eq, gte, lt, and } from 'drizzle-orm'
import { NextRequest, NextResponse } from 'next/server'

import { auth } from '@/lib/auth'
import { createDb } from '@/lib/db'
import { dailyFortune } from '@/lib/db/schema'

export async function GET(req: NextRequest) {
  try {
    const session = await auth()
    if (!session?.user?.id) return NextResponse.json({ error: '未登录' }, { status: 401 })
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    const tomorrow = new Date(today)
    tomorrow.setDate(today.getDate() + 1)
    const db = createDb()
    const result = await db
      .select()
      .from(dailyFortune)
      .where(
        and(
          eq(dailyFortune.userId, session.user.id),
          gte(dailyFortune.timestamp, today.getTime()),
          lt(dailyFortune.timestamp, tomorrow.getTime())
        )
      )
      .limit(1)
    return NextResponse.json(result[0] || {})
  } catch (e) {
    return NextResponse.json({ error: '服务器错误' }, { status: 500 })
  }
}

export async function POST(req: NextRequest) {
  try {
    const session = await auth()
    if (!session?.user?.id) return NextResponse.json({ error: '未登录' }, { status: 401 })
    const body: any = await req.json()
    const { card, interpretation, timestamp } = body
    const db = createDb()
    await db.insert(dailyFortune).values({
      userId: session.user.id,
      card: JSON.stringify(card),
      interpretation,
      timestamp
    })
    return NextResponse.json({ ok: true })
  } catch (e) {
    return NextResponse.json({ error: '服务器错误' }, { status: 500 })
  }
}
