'use client'

// import { Circle } from 'lucide-react'
import { ChevronLeft, ChevronRight } from 'lucide-react'
import { useSession } from 'next-auth/react'
import { useTranslations } from 'next-intl'
import { useEffect, useState, useRef, useCallback, useMemo, memo } from 'react'

// import { useIsMobile } from '@/hooks/use-mobile'

interface CardSelectionProps {
  requiredCount: number
  onCompleteSelection: () => void
}
interface CardTransform {
  rotation: number
  transy: number
}

// 单独的卡牌组件，使用memo优化
const Card = memo(
  ({
    index,
    transform,
    isSelected,
    onSelect,
    onMouseEnter,
    onMouseLeave
  }: {
    index: number
    transform: CardTransform
    isSelected: boolean
    onSelect: (index: number) => void
    onMouseEnter: (index: number) => void
    onMouseLeave: (index: number) => void
  }) => {
    const session = useSession()

    const touchRef = useRef(false)

    const safeCardBackUrl =
      (session.data?.user as any)?.userCardBackUrl || 'https://static.destinyai.tools/tarot/card-backs/card_bgm.png'
    const style = useMemo(
      () => ({
        zIndex: index,
        transformOrigin: `50% 500%`,
        transform: `rotate(${transform.rotation}deg) translateY(${transform.transy}%)`,
        transition: 'transform 0.2s ease-out', // 减少过渡时间，提升响应速度
        backgroundImage: `url(${safeCardBackUrl})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        willChange: 'transform' // 提示浏览器优化变换
      }),
      [transform.rotation, transform.transy, index, safeCardBackUrl]
    )

    const handleClick = useCallback(() => {
      // 如果是触摸操作，忽略点击事件
      // if (touchRef.current) {
      //   touchRef.current = false
      //   return
      // }
      onSelect(index)
    }, [index, onSelect])

    const handleMouseEnter = useCallback(() => {
      if (!isSelected && !touchRef.current) {
        onMouseEnter(index)
      }
    }, [index, isSelected, onMouseEnter])

    const handleMouseLeave = useCallback(() => {
      if (!isSelected && !touchRef.current) {
        onMouseLeave(index)
      }
    }, [index, isSelected, onMouseLeave])

    // 移动端触摸事件处理
    const touchStartPos = useRef<{ x: number; y: number } | null>(null)

    const handleTouchStart = useCallback(
      (e: React.TouchEvent) => {
        touchRef.current = true
        if (!isSelected) {
          onMouseEnter(index)
        }
        // 记录起始位置
        const touch = e.touches[0]
        touchStartPos.current = { x: touch.clientX, y: touch.clientY }
      },
      [index, isSelected, onMouseEnter]
    )

    const handleTouchEnd = useCallback(
      (e: React.TouchEvent) => {
        // 判断手指移动距离
        const touch = e.changedTouches[0]
        if (touchStartPos.current) {
          const dx = touch.clientX - touchStartPos.current.x
          const dy = touch.clientY - touchStartPos.current.y
          const distance = Math.sqrt(dx * dx + dy * dy)
          if (distance < 10) {
            // 只有移动很小才算点击
            onSelect(index)
          }
        }
        // 修复：选中卡牌后不再触发onMouseLeave，避免transy被重置为0
        // if (!isSelected) {
        //   onMouseLeave(index)
        // }
        // 延迟重置触摸标志，确保点击事件被正确忽略
        setTimeout(() => {
          touchRef.current = false
        }, 300)
        touchStartPos.current = null
      },
      [index, isSelected, onSelect]
    )

    return (
      <div
        onClick={handleClick}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        onTouchStart={handleTouchStart}
        onTouchEnd={handleTouchEnd}
        className="absolute top-0 left-0 h-48 w-30 cursor-pointer touch-manipulation rounded-lg border-2 shadow-lg"
        style={style}
      />
    )
  }
)

Card.displayName = 'Card'

export default function CardSelection({ requiredCount, onCompleteSelection }: CardSelectionProps) {
  const [selectedCard, setSelectedCard] = useState<number[]>([])
  const [showControls, setShowControls] = useState(false) // 是否显示左右按钮
  const scrollContainerRef = useRef<HTMLDivElement>(null)
  const animationFrameRef = useRef<number | undefined>(undefined)
  const t = useTranslations('draw')

  const CARD_COUNT = 78

  // const isMobile = useIsMobile()

  const [cards, setCards] = useState<CardTransform[]>(
    Array(CARD_COUNT)
      .fill(null)
      .map(() => ({ rotation: 0, transy: 0 }))
  )

  // 使用useMemo缓存扇形布局计算
  const fanRotations = useMemo(() => {
    const fanAngle = 180 // 扇形总角度
    const startAngle = -fanAngle / 2 // 起始角度

    return Array(CARD_COUNT)
      .fill(null)
      .map((_, index) => (startAngle + (index / (CARD_COUNT - 1)) * fanAngle) * 0.25) // 减少旋转角度，提升性能
  }, [CARD_COUNT])

  // 检测是否需要显示左右按钮
  const checkIfNeedControls = useCallback(() => {
    if (scrollContainerRef.current) {
      const containerWidth = scrollContainerRef.current.scrollWidth
      const viewportWidth = scrollContainerRef.current.clientWidth
      // 移动端更容易需要滚动控制
      setShowControls(containerWidth > viewportWidth)
    }
  }, [])

  // 居中显示卡牌
  const centerCards = useCallback(() => {
    if (scrollContainerRef.current) {
      const container = scrollContainerRef.current

      const scrollWidth = 1000 // 实际滚动宽度是1000
      const clientWidth = container.clientWidth // 使用容器实际宽度而不是window宽度

      // 确保容器宽度有效
      if (clientWidth === 0) {
        // 如果容器宽度为0，延迟重试
        setTimeout(() => centerCards(), 50)
        return
      }

      // 计算子 div 的中心点相对于其左边缘的距离
      const childCenter = scrollWidth / 2

      // 计算父 div 视口的中心点相对于其左边缘的距离
      const parentViewportCenter = clientWidth / 2

      // 计算需要滚动的距离，使子 div 的中心与父 div 视口的中心对齐
      // 滚动距离 = 子 div 中心点 - 父 div 视口中心点
      const scrollAmount = Math.max(0, childCenter - parentViewportCenter)

      // 使用 requestAnimationFrame 确保在下一帧执行，避免被其他操作覆盖
      requestAnimationFrame(() => {
        if (container && container.scrollLeft !== scrollAmount) {
          container.scrollLeft = scrollAmount

          // 验证滚动位置是否正确设置
          setTimeout(() => {
            if (container.scrollLeft !== scrollAmount) {
              container.scrollLeft = scrollAmount
            }
          }, 10)
        }
      })
    }
  }, [])

  // 优化的卡牌展开动画 - 使用requestAnimationFrame
  const expandCards = useCallback(() => {
    let currentIndex = 0
    const totalCards = CARD_COUNT
    // 移动端使用更快的动画
    const animationDuration = 1600 // 总动画时长（毫秒）
    const startTime = Date.now()

    const animate = () => {
      const currentTime = Date.now()
      const elapsed = currentTime - startTime
      const progress = Math.min(elapsed / animationDuration, 1)

      // 计算当前应该展开到哪张卡
      const targetIndex = Math.floor(progress * totalCards)

      if (targetIndex > currentIndex) {
        // 批量更新多张卡牌，减少状态更新频率
        const cardsToUpdate: { index: number; rotation: number }[] = []

        for (let i = currentIndex; i < targetIndex && i < totalCards; i++) {
          cardsToUpdate.push({
            index: i,
            rotation: fanRotations[i]
          })
        }

        if (cardsToUpdate.length > 0) {
          // 使用函数式更新，避免闭包问题
          setCards((prev) => {
            const newCards = [...prev]
            cardsToUpdate.forEach(({ index, rotation }) => {
              newCards[index] = { rotation, transy: 0 }
            })
            return newCards
          })
        }

        currentIndex = targetIndex
      }

      if (progress < 1) {
        animationFrameRef.current = requestAnimationFrame(animate)
      } else {
        // 动画完成后的操作，使用setTimeout确保DOM完全渲染
        checkIfNeedControls()
        centerCards()

        // setTimeout(() => {
        //   checkIfNeedControls()
        //   // 延迟居中，确保所有内容都已渲染
        //   setTimeout(() => {
        //     centerCards()
        //   }, 50)
        // }, 100)
      }
    }

    animationFrameRef.current = requestAnimationFrame(animate)
  }, [fanRotations, checkIfNeedControls, centerCards, CARD_COUNT])

  // 依次展开卡牌动画
  useEffect(() => {
    expandCards()

    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current)
      }
    }
  }, [expandCards])

  // 监听窗口大小变化
  useEffect(() => {
    const handleResize = () => {
      // 添加防抖，避免频繁触发
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current)
      }
      animationFrameRef.current = requestAnimationFrame(() => {
        checkIfNeedControls()
        // centerCards()
      })
    }

    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [checkIfNeedControls, centerCards])

  // 监听滚动位置变化，防止被意外重置
  useEffect(() => {
    const container = scrollContainerRef.current
    if (!container) return

    let lastScrollLeft = container.scrollLeft
    const handleScroll = () => {
      // 如果滚动位置被意外重置为0，重新居中
      if (container.scrollLeft === 0 && lastScrollLeft !== 0) {
        setTimeout(() => {
          // centerCards()
        }, 10)
      }
      lastScrollLeft = container.scrollLeft
    }

    container.addEventListener('scroll', handleScroll)
    return () => container.removeEventListener('scroll', handleScroll)
  }, [centerCards])

  const [isComplete, setIsComplete] = useState(false)
  useEffect(() => {
    if (requiredCount && selectedCard.length === requiredCount) {
      setIsComplete(true)
      setTimeout(() => {
        onCompleteSelection()
      }, 1000)
    }
  }, [selectedCard, onCompleteSelection, requiredCount])

  const selectCard = useCallback(
    (index: number) => {
      if (selectedCard.includes(index)) return // 防止重复选择
      if (selectedCard.length === requiredCount) return
      setCards((prev) => prev.map((card, i) => (i === index ? { ...card, transy: -110 } : card)))
      setSelectedCard((prev) => [...prev, index])
    },
    [selectedCard, requiredCount]
  )

  const handleMouseEnter = useCallback(
    (index: number) => {
      if (showControls) return
      if (selectedCard.includes(index)) return // 已选中的卡牌不响应悬停

      setCards((prev) => prev.map((card, i) => (i === index ? { ...card, transy: -10 } : card)))
    },
    [selectedCard, showControls]
  )

  const handleMouseLeave = useCallback(
    (index: number) => {
      if (selectedCard.includes(index)) return // 已选中的卡牌不响应悬停

      setCards((prev) => prev.map((card, i) => (i === index ? { ...card, transy: 0 } : card)))
    },
    [selectedCard]
  )

  // 左右滚动控制
  const handleSlideLeft = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({ left: -200, behavior: 'smooth' })
    }
  }

  const handleSlideRight = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({ left: 200, behavior: 'smooth' })
    }
  }

  return (
    <div className="relative overflow-hidden" style={{ minHeight: 'calc(100vh - 56px - env(safe-area-inset-top))' }}>
      {/* 滚动容器 */}
      {/* ${showControls ? '' : 'flex justify-center'}  */}
      <div
        ref={scrollContainerRef}
        className={`overflow-x-auto ${showControls ? '' : 'flex justify-center'}`}
        style={{
          // minHeight: 'calc(100vh - 56px - env(safe-area-inset-top))',
          /* 隐藏滚动条但保持滚动功能 */
          scrollbarWidth: 'none' /* Firefox */,
          msOverflowStyle: 'none' /* IE and Edge */,
          /* 移动端优化 */
          WebkitOverflowScrolling: 'touch' /* iOS平滑滚动 */,
          overscrollBehaviorX: 'contain' /* 防止滚动链 */,
          touchAction: 'pan-x' /* 只允许水平滚动 */,
          /* 确保滚动位置不被重置 */
          scrollBehavior: 'auto'
        }}
      >
        <style jsx>{`
          div::-webkit-scrollbar {
            display: none; /* Chrome, Safari, Opera */
          }
        `}</style>
        {/*  ${showControls ? '' : ''} */}
        <div
          className={`flex w-full min-w-[1000px] items-center justify-center`}
          style={{
            minHeight: 'calc(100vh - 56px - env(safe-area-inset-top))'
          }}
        >
          <div className="relative h-48 w-30">
            {cards.map((card, i) => (
              <Card
                key={i}
                index={i}
                transform={card}
                isSelected={selectedCard.includes(i)}
                onSelect={selectCard}
                onMouseEnter={handleMouseEnter}
                onMouseLeave={handleMouseLeave}
              />
            ))}
          </div>
        </div>
      </div>

      {/* 固定在底部的控制区域，不跟随滚动 */}
      <div className="absolute bottom-8 left-1/2 w-full max-w-md -translate-x-1/2 transform">
        <div className="mb-4 text-center text-white">
          <div className="mb-2 text-lg font-semibold">{t('selectCard', { count: String(requiredCount) })}</div>
          <div className="text-sm text-gray-300">
            {t('selected', { current: String(selectedCard.length), total: String(requiredCount) })}
          </div>
        </div>
        <p className="h-[30px] animate-pulse text-center text-xl font-bold text-yellow-400">
          {isComplete ? `✨ ${t('complete')}` : ''}
        </p>
        {/* 只在需要时显示左右按钮 */}
        <div className="h-[48px]">
          {showControls && (
            <div className="flex items-center justify-center">
              <button
                onClick={handleSlideLeft}
                className="flex h-12 w-12 touch-manipulation items-center justify-center rounded-full bg-blue-600 text-white shadow-lg transition-all duration-300 hover:bg-blue-700 active:bg-blue-800"
              >
                <ChevronLeft size={24} />
              </button>

              <div className="mx-4 text-center">
                <div className="text-xs text-gray-400">{t('leftRight')}</div>
              </div>

              <button
                onClick={handleSlideRight}
                className="flex h-12 w-12 touch-manipulation items-center justify-center rounded-full bg-blue-600 text-white shadow-lg transition-all duration-300 hover:bg-blue-700 active:bg-blue-800"
              >
                <ChevronRight size={24} />
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
